# 应用基础配置
spring:
  application:
    name: Ultimate

  # 数据源配置
  datasource:
    url: *******************************************************************************************************************************
    username: root
    password: 5247xff
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your_email_password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: cn.ilikexff.ultimate.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 服务器配置
server:
  port: 8080

# 日志配置
logging:
  level:
    cn.ilikexff.ultimate: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT配置
jwt:
  secret: UltimateReasoningSecretKey2024ForJWTTokenGeneration
  expiration: 86400000

# QQ登录配置
qq:
  app-id: your_qq_app_id
  app-key: your_qq_app_key
  redirect-uri: http://localhost:8080/auth/qq/callback

# 业务配置
ultimate:
  # 注册奖励U币
  register-reward-coins: 100
  # 每日签到基础奖励
  daily-signin-base-reward: 10
  # 每日一推额外奖励倍数
  daily-puzzle-bonus-multiplier: 1.5

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: cn.ilikexff.ultimate.controller
