-- Ultimate推理社初始数据脚本

USE ultimate_reasoning;

-- 插入初始称号数据
INSERT INTO titles (title_name, description, min_reasoning_power, acquisition_condition) VALUES
('新手侦探', '刚刚踏入推理世界的新人', 0, '注册即可获得'),
('初级推理家', '具备基础推理能力', 100, '推理力达到100'),
('中级推理家', '推理能力日渐成熟', 500, '推理力达到500'),
('高级推理家', '推理大师级别', 1000, '推理力达到1000'),
('推理宗师', '推理界的传奇人物', 5000, '推理力达到5000');

-- 插入初始勋章数据
INSERT INTO badges (badge_name, icon_url, description, acquisition_condition) VALUES
('首次登录', '/icons/first_login.png', '欢迎来到Ultimate推理社', '首次登录即可获得'),
('连续签到7天', '/icons/signin_7days.png', '坚持就是胜利', '连续签到7天'),
('连续签到30天', '/icons/signin_30days.png', '月度坚持者', '连续签到30天'),
('解谜新手', '/icons/puzzle_beginner.png', '完成第一个谜题', '完成任意一个谜题'),
('解谜达人', '/icons/puzzle_expert.png', '解谜经验丰富', '完成10个谜题'),
('解谜大师', '/icons/puzzle_master.png', '解谜界的佼佼者', '完成50个谜题'),
('每日一推完成者', '/icons/daily_puzzle.png', '每日坚持解谜', '完成任意一个每日一推');

-- 插入示例谜题数据
INSERT INTO puzzles (puzzle_name, description, background_story, clues, difficulty, required_u_coins, u_coins_reward, reasoning_power_reward, answer, puzzle_type, created_by, status, is_daily, daily_date) VALUES
('数字密码', '一个简单的数字推理谜题', '在一个古老的保险箱上，有一个4位数字密码锁。', '提示：这个数字是今年的年份', 'EASY', 0, 10, 5, '2024', 'TEXT', 1, 'PUBLISHED', false, null),

('颜色之谜', '根据颜色规律找出答案', '画家的调色板上有红、绿、蓝三种颜色。', '红+绿=黄，红+蓝=紫，绿+蓝=？', 'EASY', 0, 15, 8, '青', 'TEXT', 1, 'PUBLISHED', false, null),

('时间推理', '根据时间线索推理出答案', '侦探在案发现场发现了一个停止的时钟。', '时钟停在3:15，案发时间是下午，凶手在几点作案？', 'MEDIUM', 5, 25, 15, '15:15', 'TEXT', 1, 'PUBLISHED', false, null),

('逻辑推理', '经典的逻辑推理题', '三个人A、B、C，其中一个总说真话，一个总说假话，一个有时说真话有时说假话。', 'A说：B是说假话的人。B说：C不是说真话的人。C说：A和B中有一个是说真话的人。谁总是说真话？', 'HARD', 10, 50, 30, 'C', 'TEXT', 1, 'PUBLISHED', false, null),

('今日挑战', '今天的每日一推谜题', '这是一个特殊的每日挑战。', '1+1在什么情况下不等于2？', 'MEDIUM', 0, 30, 20, '二进制', 'TEXT', 1, 'PUBLISHED', true, CURDATE());

-- 插入提示数据
INSERT INTO hints (puzzle_id, hint_level, content, cost_u_coins, order_index) VALUES
-- 数字密码的提示
(1, 'BASIC', '这是一个四位数，与时间有关', 2, 1),
(1, 'MEDIUM', '这个数字代表当前的年份', 5, 1),
(1, 'ADVANCED', '答案是2024', 8, 1),

-- 颜色之谜的提示
(2, 'BASIC', '想想三原色的混合规律', 3, 1),
(2, 'MEDIUM', '绿色和蓝色混合会产生什么颜色？', 6, 1),

-- 时间推理的提示
(3, 'BASIC', '注意时钟显示的是12小时制还是24小时制', 5, 1),
(3, 'MEDIUM', '下午3:15用24小时制怎么表示？', 8, 1),

-- 逻辑推理的提示
(4, 'BASIC', '假设每个人的身份，看看是否矛盾', 8, 1),
(4, 'MEDIUM', '如果A总说真话，那么B的话是什么意思？', 12, 1),
(4, 'ADVANCED', '用排除法，逐一验证每种可能', 15, 1);

-- 插入管理员用户（密码：admin123，已加密）
INSERT INTO users (username, password_hash, email, nickname, u_coins, reasoning_power, registration_date, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXgwkOKRfq4ffXpq6Qs.6Qm.Ej6', '<EMAIL>', '系统管理员', 1000, 0, NOW(), 'ACTIVE');

-- 为管理员分配初始称号
INSERT INTO user_titles (user_id, title_id, acquisition_date) VALUES
(1, 1, NOW());

-- 为管理员分配初始勋章
INSERT INTO user_badges (user_id, badge_id, acquisition_date) VALUES
(1, 1, NOW());
