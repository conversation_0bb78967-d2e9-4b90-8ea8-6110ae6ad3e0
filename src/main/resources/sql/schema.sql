-- Ultimate推理社数据库建表脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS ultimate_reasoning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE ultimate_reasoning;

-- 用户表
CREATE TABLE users (
    user_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) COMMENT '密码哈希',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone_number VARCHAR(20) UNIQUE COMMENT '手机号',
    qq_openid VARCHAR(100) UNIQUE COMMENT 'QQ登录OpenID',
    nickname VARCHAR(50) NOT NULL COMMENT '昵称',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    u_coins INT NOT NULL DEFAULT 0 COMMENT 'U币数量',
    reasoning_power INT NOT NULL DEFAULT 0 COMMENT '推理力',
    current_title_id BIGINT COMMENT '当前佩戴称号ID',
    registration_date DATETIME NOT NULL COMMENT '注册时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    login_count INT NOT NULL DEFAULT 0 COMMENT '登录次数',
    status ENUM('ACTIVE', 'BANNED', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone_number),
    INDEX idx_qq_openid (qq_openid),
    INDEX idx_reasoning_power (reasoning_power)
) COMMENT '用户表';

-- 谜题表
CREATE TABLE puzzles (
    puzzle_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    puzzle_name VARCHAR(200) NOT NULL COMMENT '谜题名称',
    description TEXT NOT NULL COMMENT '谜题描述',
    background_story TEXT COMMENT '背景故事',
    clues TEXT COMMENT '线索',
    difficulty ENUM('EASY', 'MEDIUM', 'HARD', 'EXPERT') NOT NULL COMMENT '难度等级',
    required_u_coins INT NOT NULL DEFAULT 0 COMMENT '所需U币',
    u_coins_reward INT NOT NULL DEFAULT 0 COMMENT 'U币奖励',
    reasoning_power_reward INT NOT NULL DEFAULT 0 COMMENT '推理力奖励',
    answer VARCHAR(500) NOT NULL COMMENT '谜题答案',
    puzzle_type ENUM('TEXT', 'IMAGE', 'AUDIO', 'VIDEO', 'MIXED') NOT NULL DEFAULT 'TEXT' COMMENT '谜题类型',
    created_by BIGINT NOT NULL COMMENT '创建者ID',
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') NOT NULL DEFAULT 'DRAFT' COMMENT '谜题状态',
    play_count INT NOT NULL DEFAULT 0 COMMENT '游玩次数',
    completion_count INT NOT NULL DEFAULT 0 COMMENT '完成次数',
    average_rating DECIMAL(3,2) COMMENT '平均评分',
    is_daily BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为每日一推',
    daily_date DATE COMMENT '每日一推日期',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    INDEX idx_difficulty (difficulty),
    INDEX idx_puzzle_type (puzzle_type),
    INDEX idx_status (status),
    INDEX idx_is_daily (is_daily),
    INDEX idx_daily_date (daily_date),
    INDEX idx_created_by (created_by)
) COMMENT '谜题表';

-- 用户谜题进度表
CREATE TABLE user_puzzle_progress (
    progress_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    puzzle_id BIGINT NOT NULL COMMENT '谜题ID',
    status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED') NOT NULL DEFAULT 'NOT_STARTED' COMMENT '进度状态',
    attempts_count INT NOT NULL DEFAULT 0 COMMENT '尝试次数',
    completion_date DATETIME COMMENT '完成时间',
    earned_u_coins INT NOT NULL DEFAULT 0 COMMENT '获得的U币',
    earned_reasoning_power INT NOT NULL DEFAULT 0 COMMENT '获得的推理力',
    start_time DATETIME COMMENT '开始时间',
    total_time_spent BIGINT COMMENT '总耗时（秒）',
    hints_used INT NOT NULL DEFAULT 0 COMMENT '使用提示次数',
    rating INT COMMENT '用户评分(1-5)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    UNIQUE KEY uk_user_puzzle (user_id, puzzle_id),
    INDEX idx_user_id (user_id),
    INDEX idx_puzzle_id (puzzle_id),
    INDEX idx_status (status)
) COMMENT '用户谜题进度表';

-- 提示表
CREATE TABLE hints (
    hint_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    puzzle_id BIGINT NOT NULL COMMENT '谜题ID',
    hint_level ENUM('BASIC', 'MEDIUM', 'ADVANCED') NOT NULL COMMENT '提示等级',
    content TEXT NOT NULL COMMENT '提示内容',
    cost_u_coins INT NOT NULL COMMENT '消耗U币',
    order_index INT NOT NULL DEFAULT 1 COMMENT '排序',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    INDEX idx_puzzle_id (puzzle_id),
    INDEX idx_hint_level (hint_level)
) COMMENT '提示表';

-- 用户提示记录表
CREATE TABLE user_hint_log (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    hint_id BIGINT NOT NULL COMMENT '提示ID',
    purchase_time DATETIME NOT NULL COMMENT '购买时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    INDEX idx_user_id (user_id),
    INDEX idx_hint_id (hint_id),
    INDEX idx_purchase_time (purchase_time)
) COMMENT '用户提示记录表';

-- 称号表
CREATE TABLE titles (
    title_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title_name VARCHAR(100) NOT NULL COMMENT '称号名称',
    description TEXT COMMENT '称号描述',
    min_reasoning_power INT COMMENT '获得所需最低推理力',
    acquisition_condition TEXT COMMENT '获取条件描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    INDEX idx_min_reasoning_power (min_reasoning_power)
) COMMENT '称号表';

-- 勋章表
CREATE TABLE badges (
    badge_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    badge_name VARCHAR(100) NOT NULL COMMENT '勋章名称',
    icon_url VARCHAR(500) COMMENT '图标URL',
    description TEXT COMMENT '勋章描述',
    acquisition_condition TEXT COMMENT '获取条件描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除'
) COMMENT '勋章表';

-- 用户称号关联表
CREATE TABLE user_titles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    title_id BIGINT NOT NULL COMMENT '称号ID',
    acquisition_date DATETIME NOT NULL COMMENT '获得时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    UNIQUE KEY uk_user_title (user_id, title_id),
    INDEX idx_user_id (user_id),
    INDEX idx_title_id (title_id)
) COMMENT '用户称号关联表';

-- 用户勋章关联表
CREATE TABLE user_badges (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    badge_id BIGINT NOT NULL COMMENT '勋章ID',
    acquisition_date DATETIME NOT NULL COMMENT '获得时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    UNIQUE KEY uk_user_badge (user_id, badge_id),
    INDEX idx_user_id (user_id),
    INDEX idx_badge_id (badge_id)
) COMMENT '用户勋章关联表';

-- 用户签到记录表
CREATE TABLE user_sign_in (
    sign_in_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    sign_in_date DATE NOT NULL COMMENT '签到日期',
    reward_u_coins INT NOT NULL DEFAULT 0 COMMENT '获得的U币奖励',
    consecutive_days INT NOT NULL DEFAULT 1 COMMENT '连续签到天数',
    is_double_reward BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否双倍奖励',
    reward_type ENUM('NORMAL', 'WEEKLY_BONUS', 'MONTHLY_BONUS') NOT NULL DEFAULT 'NORMAL' COMMENT '奖励类型',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    UNIQUE KEY uk_user_date (user_id, sign_in_date),
    INDEX idx_user_id (user_id),
    INDEX idx_sign_in_date (sign_in_date),
    INDEX idx_consecutive_days (consecutive_days)
) COMMENT '用户签到记录表';

-- 签到配置表
CREATE TABLE sign_in_config (
    config_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    day_number INT NOT NULL COMMENT '第几天（1-7为一周循环）',
    base_reward INT NOT NULL DEFAULT 5 COMMENT '基础奖励U币',
    bonus_reward INT NOT NULL DEFAULT 0 COMMENT '额外奖励U币',
    reward_type ENUM('NORMAL', 'WEEKLY_BONUS', 'MONTHLY_BONUS') NOT NULL DEFAULT 'NORMAL' COMMENT '奖励类型',
    description VARCHAR(200) COMMENT '奖励描述',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',

    UNIQUE KEY uk_day_number (day_number),
    INDEX idx_is_active (is_active)
) COMMENT '签到配置表';
