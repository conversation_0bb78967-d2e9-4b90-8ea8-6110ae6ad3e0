package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.dto.PuzzleListResponse;
import cn.ilikexff.ultimate.entity.Puzzle;
import cn.ilikexff.ultimate.service.PuzzleService;
import cn.ilikexff.ultimate.service.UserDetailsServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 谜题控制器
 */
@Tag(name = "谜题管理", description = "谜题相关接口")
@Slf4j
@RestController
@RequestMapping("/api/puzzles")
@RequiredArgsConstructor
public class PuzzleController {

    private final PuzzleService puzzleService;

    /**
     * 获取谜题列表
     */
    @Operation(summary = "获取谜题列表", description = "分页获取谜题列表，支持难度筛选")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/list")
    public Result<PuzzleListResponse> getPuzzleList(
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页数量", example = "10")
            @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "难度筛选", example = "EASY")
            @RequestParam(required = false) String difficulty) {

        Long userId = getCurrentUserIdOrNull();
        PuzzleListResponse response = puzzleService.getPuzzleList(page, size, difficulty, userId);
        return Result.success(response);
    }

    /**
     * 获取谜题详情
     */
    @GetMapping("/{puzzleId}")
    public Result<Puzzle> getPuzzleDetail(@PathVariable Long puzzleId) {
        Puzzle puzzle = puzzleService.getPuzzleById(puzzleId);

        // 隐藏答案信息
        puzzle.setAnswer(null);

        return Result.success(puzzle);
    }

    /**
     * 获取今日推荐谜题
     */
    @GetMapping("/daily")
    public Result<Puzzle> getDailyPuzzle() {
        Puzzle puzzle = puzzleService.getDailyPuzzle();

        // 隐藏答案信息
        puzzle.setAnswer(null);

        return Result.success(puzzle);
    }

    /**
     * 开始解谜
     */
    @Operation(summary = "开始解谜", description = "开始解答指定谜题")
    @ApiResponse(responseCode = "200", description = "开始成功")
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/{puzzleId}/start")
    public Result<Void> startPuzzle(
            @Parameter(description = "谜题ID", required = true)
            @PathVariable Long puzzleId) {
        Long userId = getCurrentUserId();
        puzzleService.startPuzzle(puzzleId, userId);
        return Result.success("开始解谜");
    }

    /**
     * 提交答案
     */
    @PostMapping("/{puzzleId}/submit")
    public Result<Map<String, Object>> submitAnswer(
            @PathVariable Long puzzleId,
            @RequestBody Map<String, String> request) {

        Long userId = getCurrentUserId();
        String answer = request.get("answer");

        boolean isCorrect = puzzleService.submitAnswer(puzzleId, userId, answer);

        Map<String, Object> result = new HashMap<>();
        result.put("correct", isCorrect);
        result.put("message", isCorrect ? "恭喜你，答案正确！" : "答案不正确，请再试试");

        return Result.success(result);
    }

    /**
     * 获取谜题提示
     */
    @PostMapping("/{puzzleId}/hint")
    public Result<Map<String, Object>> getPuzzleHint(
            @PathVariable Long puzzleId,
            @RequestBody Map<String, String> request) {

        Long userId = getCurrentUserId();
        String hintLevel = request.get("level");

        String hintContent = puzzleService.getPuzzleHint(puzzleId, hintLevel, userId);

        Map<String, Object> result = new HashMap<>();
        result.put("hint", hintContent);

        return Result.success("获取提示成功", result);
    }

    /**
     * 评价谜题
     */
    @PostMapping("/{puzzleId}/rate")
    public Result<Void> ratePuzzle(
            @PathVariable Long puzzleId,
            @RequestBody Map<String, Integer> request) {

        Long userId = getCurrentUserId();
        Integer rating = request.get("rating");

        // TODO: 实现谜题评价逻辑

        return Result.success("评价成功");
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsServiceImpl.UserPrincipal) {
            UserDetailsServiceImpl.UserPrincipal userPrincipal =
                    (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
            return userPrincipal.getUser().getUserId();
        }
        throw new RuntimeException("用户未登录");
    }

    /**
     * 获取当前登录用户ID（可能为null）
     */
    private Long getCurrentUserIdOrNull() {
        try {
            return getCurrentUserId();
        } catch (Exception e) {
            return null;
        }
    }
}
