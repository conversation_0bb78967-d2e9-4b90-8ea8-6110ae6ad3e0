package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.dto.LoginRequest;
import cn.ilikexff.ultimate.dto.RegisterRequest;
import cn.ilikexff.ultimate.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthService authService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<Map<String, Object>> register(@Valid @RequestBody RegisterRequest request) {
        log.info("User registration request: {}", request.getUsername());
        
        Map<String, Object> result = authService.register(request);
        return Result.success("注册成功", result);
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@Valid @RequestBody LoginRequest request) {
        log.info("User login request: {}", request.getUsername());
        
        Map<String, Object> result = authService.login(request);
        return Result.success("登录成功", result);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        // JWT是无状态的，客户端删除token即可
        return Result.success("登出成功");
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        // TODO: 实现用户名检查逻辑
        return Result.success(true);
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        // TODO: 实现邮箱检查逻辑
        return Result.success(true);
    }

    /**
     * 发送验证码
     */
    @PostMapping("/send-code")
    public Result<Void> sendVerificationCode(@RequestParam String target, @RequestParam String type) {
        // TODO: 实现验证码发送逻辑
        log.info("Send verification code: target={}, type={}", target, type);
        return Result.success("验证码已发送");
    }
}
