package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.entity.DailyPuzzle;
import cn.ilikexff.ultimate.entity.UserDailyPuzzleCompletion;
import cn.ilikexff.ultimate.service.DailyPuzzleService;
import cn.ilikexff.ultimate.service.UserDetailsServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 每日一推控制器
 */
@Tag(name = "每日一推", description = "每日推荐谜题相关接口")
@Slf4j
@RestController
@RequestMapping("/api/daily-puzzle")
@RequiredArgsConstructor
public class DailyPuzzleController {

    private final DailyPuzzleService dailyPuzzleService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || 
            "anonymousUser".equals(authentication.getPrincipal())) {
            return null;
        }
        UserDetailsServiceImpl.UserPrincipal userPrincipal = 
                (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
        return userPrincipal.getUser().getUserId();
    }

    /**
     * 获取今日一推
     */
    @Operation(summary = "获取今日一推", description = "获取今天的推荐谜题")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/today")
    public Result<DailyPuzzleService.DailyPuzzleInfo> getTodayPuzzle() {
        Long userId = getCurrentUserId();
        DailyPuzzleService.DailyPuzzleInfo todayPuzzle = dailyPuzzleService.getTodayPuzzle(userId);
        return Result.success(todayPuzzle);
    }

    /**
     * 获取用户每日一推统计
     */
    @Operation(summary = "获取每日一推统计", description = "获取用户的每日一推完成统计")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/statistics")
    public Result<DailyPuzzleService.DailyPuzzleStatistics> getDailyPuzzleStatistics() {
        Long userId = getCurrentUserId();
        if (userId == null) {
            return Result.error("请先登录");
        }
        
        DailyPuzzleService.DailyPuzzleStatistics statistics = 
                dailyPuzzleService.getUserDailyPuzzleStatistics(userId);
        return Result.success(statistics);
    }

    /**
     * 获取用户完成历史
     */
    @Operation(summary = "获取完成历史", description = "获取用户的每日一推完成历史")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @SecurityRequirement(name = "bearerAuth")
    @GetMapping("/history")
    public Result<List<UserDailyPuzzleCompletion>> getCompletionHistory() {
        Long userId = getCurrentUserId();
        if (userId == null) {
            return Result.error("请先登录");
        }
        
        List<UserDailyPuzzleCompletion> history = dailyPuzzleService.getUserCompletionHistory(userId);
        return Result.success(history);
    }

    /**
     * 获取历史每日一推
     */
    @Operation(summary = "获取历史每日一推", description = "获取最近的每日一推列表")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/recent")
    public Result<List<DailyPuzzle>> getRecentDailyPuzzles(
            @Parameter(description = "限制返回数量", example = "7")
            @RequestParam(defaultValue = "7") int limit) {
        
        List<DailyPuzzle> recentPuzzles = dailyPuzzleService.getRecentDailyPuzzles(limit);
        return Result.success(recentPuzzles);
    }

    /**
     * 手动生成今日一推（管理员功能）
     */
    @Operation(summary = "生成今日一推", description = "手动生成今天的每日一推（管理员功能）")
    @ApiResponse(responseCode = "200", description = "生成成功")
    @SecurityRequirement(name = "bearerAuth")
    @PostMapping("/generate")
    public Result<DailyPuzzle> generateTodayPuzzle() {
        // TODO: 添加管理员权限检查
        DailyPuzzle dailyPuzzle = dailyPuzzleService.generateTodayPuzzle();
        return Result.success("今日一推生成成功", dailyPuzzle);
    }
}
