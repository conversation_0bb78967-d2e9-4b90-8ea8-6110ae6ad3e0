package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.entity.Badge;
import cn.ilikexff.ultimate.entity.Title;
import cn.ilikexff.ultimate.service.AchievementService;
import cn.ilikexff.ultimate.service.UserDetailsServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 成就控制器
 */
@Tag(name = "成就管理", description = "称号和勋章相关接口")
@Slf4j
@RestController
@RequestMapping("/api/achievements")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
public class AchievementController {

    private final AchievementService achievementService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsServiceImpl.UserPrincipal userPrincipal = 
                (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
        return userPrincipal.getUser().getUserId();
    }

    /**
     * 获取所有称号
     */
    @Operation(summary = "获取所有称号", description = "获取系统中所有可用的称号")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/titles")
    public Result<List<Title>> getAllTitles() {
        List<Title> titles = achievementService.getAllTitles();
        return Result.success(titles);
    }

    /**
     * 获取用户拥有的称号
     */
    @Operation(summary = "获取用户称号", description = "获取当前用户拥有的所有称号")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/my-titles")
    public Result<List<Title>> getUserTitles() {
        Long userId = getCurrentUserId();
        List<Title> titles = achievementService.getUserTitles(userId);
        return Result.success(titles);
    }

    /**
     * 获取所有勋章
     */
    @Operation(summary = "获取所有勋章", description = "获取系统中所有可用的勋章")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/badges")
    public Result<List<Badge>> getAllBadges() {
        List<Badge> badges = achievementService.getAllBadges();
        return Result.success(badges);
    }

    /**
     * 获取用户拥有的勋章
     */
    @Operation(summary = "获取用户勋章", description = "获取当前用户拥有的所有勋章")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/my-badges")
    public Result<List<Badge>> getUserBadges() {
        Long userId = getCurrentUserId();
        List<Badge> badges = achievementService.getUserBadges(userId);
        return Result.success(badges);
    }

    /**
     * 获取用户成就统计
     */
    @Operation(summary = "获取成就统计", description = "获取当前用户的成就统计信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/statistics")
    public Result<AchievementService.AchievementStatistics> getAchievementStatistics() {
        Long userId = getCurrentUserId();
        AchievementService.AchievementStatistics statistics = achievementService.getUserAchievementStatistics(userId);
        return Result.success(statistics);
    }

    /**
     * 手动检查并更新用户称号
     */
    @Operation(summary = "检查用户称号", description = "手动检查并更新用户可获得的称号")
    @ApiResponse(responseCode = "200", description = "检查完成")
    @PostMapping("/check-titles")
    public Result<List<Title>> checkAndUpdateUserTitles() {
        Long userId = getCurrentUserId();
        List<Title> newTitles = achievementService.checkAndUpdateUserTitles(userId);
        
        if (newTitles.isEmpty()) {
            return Result.success("暂无新称号可获得", newTitles);
        } else {
            return Result.success("恭喜获得新称号！", newTitles);
        }
    }
}
