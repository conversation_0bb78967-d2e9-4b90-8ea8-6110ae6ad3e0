package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.dto.UserProfileResponse;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.service.UserDetailsServiceImpl;
import cn.ilikexff.ultimate.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/profile")
    public Result<UserProfileResponse> getCurrentUserProfile() {
        Long userId = getCurrentUserId();
        UserProfileResponse profile = userService.getUserProfile(userId);
        return Result.success(profile);
    }

    /**
     * 获取指定用户信息
     */
    @GetMapping("/profile/{userId}")
    public Result<UserProfileResponse> getUserProfile(@PathVariable Long userId) {
        UserProfileResponse profile = userService.getUserProfile(userId);
        return Result.success(profile);
    }

    /**
     * 更新用户基本信息
     */
    @PutMapping("/profile")
    public Result<Void> updateUserProfile(@RequestBody Map<String, Object> request) {
        Long userId = getCurrentUserId();

        String nickname = (String) request.get("nickname");
        String avatarUrl = (String) request.get("avatarUrl");

        userService.updateUserInfo(userId, nickname, avatarUrl);
        return Result.success("更新成功");
    }

    /**
     * 获取用户U币余额
     */
    @GetMapping("/coins")
    public Result<Map<String, Object>> getUserCoins() {
        Long userId = getCurrentUserId();
        User user = userService.getUserById(userId);

        Map<String, Object> result = new HashMap<>();
        result.put("uCoins", user.getUCoins());
        result.put("reasoningPower", user.getReasoningPower());

        return Result.success(result);
    }

    /**
     * 每日签到
     */
    @PostMapping("/daily-signin")
    public Result<Map<String, Object>> dailySignin() {
        // TODO: 实现每日签到逻辑
        Long userId = getCurrentUserId();

        Map<String, Object> result = new HashMap<>();
        result.put("reward", 10);
        result.put("message", "签到成功，获得10个U币");

        return Result.success("签到成功", result);
    }

    /**
     * 获取用户成就统计
     */
    @GetMapping("/achievements")
    public Result<Map<String, Object>> getUserAchievements() {
        Long userId = getCurrentUserId();

        // TODO: 实现成就统计逻辑
        Map<String, Object> achievements = new HashMap<>();
        achievements.put("completedPuzzles", 0);
        achievements.put("totalReasoningPower", 0);
        achievements.put("titlesCount", 0);
        achievements.put("badgesCount", 0);

        return Result.success(achievements);
    }

    /**
     * 获取推理力排行榜
     */
    @GetMapping("/ranking")
    public Result<List<User>> getReasoningPowerRanking(@RequestParam(defaultValue = "10") int limit) {
        List<User> ranking = userService.getReasoningPowerRanking(limit);
        return Result.success(ranking);
    }

    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsServiceImpl.UserPrincipal) {
            UserDetailsServiceImpl.UserPrincipal userPrincipal =
                    (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
            return userPrincipal.getUser().getUserId();
        }
        throw new RuntimeException("用户未登录");
    }
}
