package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.entity.SignInConfig;
import cn.ilikexff.ultimate.entity.UserSignIn;
import cn.ilikexff.ultimate.service.SignInService;
import cn.ilikexff.ultimate.service.UserDetailsServiceImpl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 签到控制器
 */
@Tag(name = "签到管理", description = "每日签到相关接口")
@Slf4j
@RestController
@RequestMapping("/api/signin")
@RequiredArgsConstructor
@SecurityRequirement(name = "bearerAuth")
public class SignInController {

    private final SignInService signInService;

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsServiceImpl.UserPrincipal userPrincipal = 
                (UserDetailsServiceImpl.UserPrincipal) authentication.getPrincipal();
        return userPrincipal.getUser().getUserId();
    }

    /**
     * 每日签到
     */
    @Operation(summary = "每日签到", description = "用户每日签到获取奖励")
    @ApiResponse(responseCode = "200", description = "签到成功")
    @PostMapping("/daily")
    public Result<SignInService.SignInResult> dailySignIn() {
        Long userId = getCurrentUserId();
        SignInService.SignInResult result = signInService.signIn(userId);
        return Result.success("签到成功！", result);
    }

    /**
     * 检查今日签到状态
     */
    @Operation(summary = "检查签到状态", description = "检查用户今日是否已签到")
    @ApiResponse(responseCode = "200", description = "检查成功")
    @GetMapping("/status")
    public Result<Boolean> checkSignInStatus() {
        Long userId = getCurrentUserId();
        boolean hasSignedIn = signInService.hasSignedInToday(userId);
        return Result.success(hasSignedIn);
    }

    /**
     * 获取签到统计
     */
    @Operation(summary = "获取签到统计", description = "获取用户的签到统计信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/statistics")
    public Result<SignInService.SignInStatistics> getSignInStatistics() {
        Long userId = getCurrentUserId();
        SignInService.SignInStatistics statistics = signInService.getUserSignInStatistics(userId);
        return Result.success(statistics);
    }

    /**
     * 获取签到历史
     */
    @Operation(summary = "获取签到历史", description = "获取用户的签到历史记录")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/history")
    public Result<List<UserSignIn>> getSignInHistory(
            @Parameter(description = "限制返回数量，0表示不限制", example = "30")
            @RequestParam(defaultValue = "30") int limit) {
        Long userId = getCurrentUserId();
        List<UserSignIn> history = signInService.getUserSignInHistory(userId, limit);
        return Result.success(history);
    }

    /**
     * 获取月度签到日历
     */
    @Operation(summary = "获取月度签到日历", description = "获取指定月份的签到日历")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/calendar")
    public Result<List<LocalDate>> getMonthlySignInCalendar(
            @Parameter(description = "月份，格式：yyyy-MM-dd，默认为当前月", example = "2024-01-01")
            @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate month) {
        
        Long userId = getCurrentUserId();
        LocalDate targetMonth = month != null ? month : LocalDate.now();
        List<LocalDate> calendar = signInService.getMonthlySignInCalendar(userId, targetMonth);
        return Result.success(calendar);
    }

    /**
     * 获取签到配置
     */
    @Operation(summary = "获取签到配置", description = "获取签到奖励配置信息")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/config")
    public Result<List<SignInConfig>> getSignInConfigs() {
        List<SignInConfig> configs = signInService.getSignInConfigs();
        return Result.success(configs);
    }
}
