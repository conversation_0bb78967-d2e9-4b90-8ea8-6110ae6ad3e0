package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 排行榜控制器
 */
@Tag(name = "排行榜", description = "排行榜相关接口")
@Slf4j
@RestController
@RequestMapping("/api/ranking")
@RequiredArgsConstructor
public class RankingController {

    private final UserService userService;

    /**
     * 获取推理力排行榜
     */
    @Operation(summary = "获取推理力排行榜", description = "获取推理力排行榜前N名用户")
    @ApiResponse(responseCode = "200", description = "获取成功")
    @GetMapping("/reasoning-power")
    public Result<List<User>> getReasoningPowerRanking(
            @Parameter(description = "排行榜数量限制", example = "10")
            @RequestParam(defaultValue = "10") int limit) {
        if (limit > 100) {
            limit = 100; // 限制最大查询数量
        }

        List<User> ranking = userService.getReasoningPowerRanking(limit);
        return Result.success(ranking);
    }
}
