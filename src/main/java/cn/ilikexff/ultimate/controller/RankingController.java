package cn.ilikexff.ultimate.controller;

import cn.ilikexff.ultimate.common.Result;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 排行榜控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ranking")
@RequiredArgsConstructor
public class RankingController {

    private final UserService userService;

    /**
     * 获取推理力排行榜
     */
    @GetMapping("/reasoning-power")
    public Result<List<User>> getReasoningPowerRanking(@RequestParam(defaultValue = "10") int limit) {
        if (limit > 100) {
            limit = 100; // 限制最大查询数量
        }
        
        List<User> ranking = userService.getReasoningPowerRanking(limit);
        return Result.success(ranking);
    }
}
