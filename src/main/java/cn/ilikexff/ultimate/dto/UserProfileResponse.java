package cn.ilikexff.ultimate.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户个人资料响应DTO
 */
@Data
public class UserProfileResponse {
    
    private Long userId;
    private String username;
    private String nickname;
    private String email;
    private String phoneNumber;
    private String avatarUrl;
    private Integer uCoins;
    private Integer reasoningPower;
    private String currentTitle;
    private LocalDateTime registrationDate;
    private LocalDateTime lastLoginTime;
    private Integer loginCount;
    
    // 成就统计
    private Integer completedPuzzlesCount;
    private Integer totalPuzzlesCount;
    private Integer ownedTitlesCount;
    private Integer ownedBadgesCount;
    
    // 称号和勋章列表
    private List<TitleInfo> titles;
    private List<BadgeInfo> badges;
    
    @Data
    public static class TitleInfo {
        private Long titleId;
        private String titleName;
        private String description;
        private LocalDateTime acquisitionDate;
        private Boolean isCurrent;
    }
    
    @Data
    public static class BadgeInfo {
        private Long badgeId;
        private String badgeName;
        private String iconUrl;
        private String description;
        private LocalDateTime acquisitionDate;
    }
}
