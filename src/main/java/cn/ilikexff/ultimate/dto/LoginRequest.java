package cn.ilikexff.ultimate.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

/**
 * 登录请求DTO
 */
@Schema(description = "用户登录请求")
@Data
public class LoginRequest {

    @Schema(description = "用户名（支持用户名/邮箱/手机号）", example = "testuser", required = true)
    @NotBlank(message = "用户名不能为空")
    private String username; // 可以是用户名、邮箱或手机号

    @Schema(description = "密码", example = "123456", required = true)
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "是否记住登录状态", example = "false")
    private Boolean rememberMe = false; // 记住登录状态
}
