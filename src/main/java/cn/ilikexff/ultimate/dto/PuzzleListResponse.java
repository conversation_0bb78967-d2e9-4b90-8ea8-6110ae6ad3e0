package cn.ilikexff.ultimate.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 谜题列表响应DTO
 */
@Data
public class PuzzleListResponse {
    
    private List<PuzzleInfo> puzzles;
    private Integer total;
    private Integer page;
    private Integer size;
    private Integer totalPages;
    
    @Data
    public static class PuzzleInfo {
        private Long puzzleId;
        private String puzzleName;
        private String description;
        private String difficulty;
        private Integer requiredUCoins;
        private Integer uCoinsReward;
        private Integer reasoningPowerReward;
        private String puzzleType;
        private Integer playCount;
        private Integer completionCount;
        private Double averageRating;
        private Boolean isDaily;
        private LocalDateTime createdAt;
        
        // 用户相关信息（需要登录）
        private String userStatus; // NOT_STARTED, IN_PROGRESS, COMPLETED
        private Boolean canPlay; // 是否可以游玩（U币是否足够）
        private Integer userRating; // 用户评分
    }
}
