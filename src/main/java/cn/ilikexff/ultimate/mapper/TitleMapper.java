package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.Title;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 称号Mapper接口
 */
@Mapper
public interface TitleMapper {

    /**
     * 根据ID查询称号
     */
    @Select("SELECT * FROM titles WHERE title_id = #{titleId} AND deleted = false")
    Title findById(Long titleId);

    /**
     * 查询所有称号
     */
    @Select("SELECT * FROM titles WHERE deleted = false ORDER BY min_reasoning_power ASC")
    List<Title> findAll();

    /**
     * 根据推理力查询可获得的称号
     */
    @Select("SELECT * FROM titles WHERE min_reasoning_power <= #{reasoningPower} AND deleted = false ORDER BY min_reasoning_power DESC")
    List<Title> findAvailableByReasoningPower(Integer reasoningPower);

    /**
     * 根据推理力查询最高可获得的称号
     */
    @Select("SELECT * FROM titles WHERE min_reasoning_power <= #{reasoningPower} AND deleted = false ORDER BY min_reasoning_power DESC LIMIT 1")
    Title findHighestAvailableByReasoningPower(Integer reasoningPower);

    /**
     * 插入称号
     */
    @Insert("INSERT INTO titles (title_name, description, min_reasoning_power, acquisition_condition, created_at, updated_at, deleted) " +
            "VALUES (#{titleName}, #{description}, #{minReasoningPower}, #{acquisitionCondition}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "titleId")
    int insert(Title title);

    /**
     * 更新称号
     */
    @Update("UPDATE titles SET title_name = #{titleName}, description = #{description}, " +
            "min_reasoning_power = #{minReasoningPower}, acquisition_condition = #{acquisitionCondition}, " +
            "updated_at = NOW() WHERE title_id = #{titleId}")
    int update(Title title);

    /**
     * 软删除称号
     */
    @Update("UPDATE titles SET deleted = true, updated_at = NOW() WHERE title_id = #{titleId}")
    int deleteById(Long titleId);

    /**
     * 统计称号数量
     */
    @Select("SELECT COUNT(*) FROM titles WHERE deleted = false")
    int count();
}
