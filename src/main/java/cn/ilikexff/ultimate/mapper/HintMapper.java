package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.Hint;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 提示Mapper接口
 */
@Mapper
public interface HintMapper {

    /**
     * 根据ID查询提示
     */
    @Select("SELECT * FROM hints WHERE hint_id = #{hintId} AND deleted = false")
    Hint findById(Long hintId);

    /**
     * 根据谜题ID查询所有提示
     */
    @Select("SELECT * FROM hints WHERE puzzle_id = #{puzzleId} AND deleted = false ORDER BY hint_level, order_index")
    List<Hint> findByPuzzleId(Long puzzleId);

    /**
     * 根据谜题ID和提示等级查询提示
     */
    @Select("SELECT * FROM hints WHERE puzzle_id = #{puzzleId} AND hint_level = #{hintLevel} AND deleted = false ORDER BY order_index")
    List<Hint> findByPuzzleIdAndLevel(@Param("puzzleId") Long puzzleId, @Param("hintLevel") String hintLevel);

    /**
     * 根据谜题ID和提示等级查询第一个提示
     */
    @Select("SELECT * FROM hints WHERE puzzle_id = #{puzzleId} AND hint_level = #{hintLevel} AND deleted = false ORDER BY order_index LIMIT 1")
    Hint findFirstByPuzzleIdAndLevel(@Param("puzzleId") Long puzzleId, @Param("hintLevel") String hintLevel);

    /**
     * 插入提示
     */
    @Insert("INSERT INTO hints (puzzle_id, hint_level, content, cost_u_coins, order_index, created_at, updated_at, deleted) " +
            "VALUES (#{puzzleId}, #{hintLevel}, #{content}, #{costUCoins}, #{orderIndex}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "hintId")
    int insert(Hint hint);

    /**
     * 更新提示
     */
    @Update("UPDATE hints SET puzzle_id = #{puzzleId}, hint_level = #{hintLevel}, content = #{content}, " +
            "cost_u_coins = #{costUCoins}, order_index = #{orderIndex}, updated_at = NOW() " +
            "WHERE hint_id = #{hintId}")
    int update(Hint hint);

    /**
     * 软删除提示
     */
    @Update("UPDATE hints SET deleted = true, updated_at = NOW() WHERE hint_id = #{hintId}")
    int deleteById(Long hintId);

    /**
     * 统计谜题的提示数量
     */
    @Select("SELECT COUNT(*) FROM hints WHERE puzzle_id = #{puzzleId} AND deleted = false")
    int countByPuzzleId(Long puzzleId);

    /**
     * 根据等级统计谜题的提示数量
     */
    @Select("SELECT COUNT(*) FROM hints WHERE puzzle_id = #{puzzleId} AND hint_level = #{hintLevel} AND deleted = false")
    int countByPuzzleIdAndLevel(@Param("puzzleId") Long puzzleId, @Param("hintLevel") String hintLevel);
}
