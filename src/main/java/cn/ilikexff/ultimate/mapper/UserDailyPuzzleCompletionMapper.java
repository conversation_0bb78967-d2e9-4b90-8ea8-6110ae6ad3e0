package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.UserDailyPuzzleCompletion;
import org.apache.ibatis.annotations.*;
import java.time.LocalDate;
import java.util.List;

/**
 * 用户每日一推完成记录Mapper接口
 */
@Mapper
public interface UserDailyPuzzleCompletionMapper {

    /**
     * 根据ID查询完成记录
     */
    @Select("SELECT * FROM user_daily_puzzle_completions WHERE completion_id = #{completionId} AND deleted = false")
    UserDailyPuzzleCompletion findById(Long completionId);

    /**
     * 根据用户ID和每日一推ID查询完成记录
     */
    @Select("SELECT * FROM user_daily_puzzle_completions WHERE user_id = #{userId} AND daily_id = #{dailyId} AND deleted = false")
    UserDailyPuzzleCompletion findByUserIdAndDailyId(@Param("userId") Long userId, @Param("dailyId") Long dailyId);

    /**
     * 根据用户ID和日期查询完成记录
     */
    @Select("SELECT udpc.* FROM user_daily_puzzle_completions udpc " +
            "JOIN daily_puzzles dp ON udpc.daily_id = dp.daily_id " +
            "WHERE udpc.user_id = #{userId} AND dp.puzzle_date = #{date} AND udpc.deleted = false")
    UserDailyPuzzleCompletion findByUserIdAndDate(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 根据用户ID查询所有完成记录
     */
    @Select("SELECT * FROM user_daily_puzzle_completions WHERE user_id = #{userId} AND deleted = false ORDER BY completion_date DESC")
    List<UserDailyPuzzleCompletion> findByUserId(Long userId);

    /**
     * 根据每日一推ID查询所有完成记录
     */
    @Select("SELECT * FROM user_daily_puzzle_completions WHERE daily_id = #{dailyId} AND deleted = false ORDER BY created_at")
    List<UserDailyPuzzleCompletion> findByDailyId(Long dailyId);

    /**
     * 查询用户指定日期范围内的完成记录
     */
    @Select("SELECT * FROM user_daily_puzzle_completions WHERE user_id = #{userId} " +
            "AND completion_date BETWEEN #{startDate} AND #{endDate} AND deleted = false " +
            "ORDER BY completion_date DESC")
    List<UserDailyPuzzleCompletion> findByUserIdAndDateRange(@Param("userId") Long userId,
                                                             @Param("startDate") LocalDate startDate,
                                                             @Param("endDate") LocalDate endDate);

    /**
     * 检查用户是否已完成指定日期的每日一推
     */
    @Select("SELECT COUNT(*) > 0 FROM user_daily_puzzle_completions udpc " +
            "JOIN daily_puzzles dp ON udpc.daily_id = dp.daily_id " +
            "WHERE udpc.user_id = #{userId} AND dp.puzzle_date = #{date} AND udpc.deleted = false")
    boolean hasCompletedByDate(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 检查用户今天是否已完成每日一推
     */
    @Select("SELECT COUNT(*) > 0 FROM user_daily_puzzle_completions udpc " +
            "JOIN daily_puzzles dp ON udpc.daily_id = dp.daily_id " +
            "WHERE udpc.user_id = #{userId} AND dp.puzzle_date = CURDATE() AND udpc.deleted = false")
    boolean hasCompletedToday(Long userId);

    /**
     * 插入完成记录
     */
    @Insert("INSERT INTO user_daily_puzzle_completions (user_id, daily_id, completion_date, " +
            "earned_u_coins, earned_reasoning_power, time_spent, attempts_count, " +
            "created_at, updated_at, deleted) " +
            "VALUES (#{userId}, #{dailyId}, #{completionDate}, #{earnedUCoins}, " +
            "#{earnedReasoningPower}, #{timeSpent}, #{attemptsCount}, " +
            "#{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "completionId")
    int insert(UserDailyPuzzleCompletion completion);

    /**
     * 更新完成记录
     */
    @Update("UPDATE user_daily_puzzle_completions SET user_id = #{userId}, daily_id = #{dailyId}, " +
            "completion_date = #{completionDate}, earned_u_coins = #{earnedUCoins}, " +
            "earned_reasoning_power = #{earnedReasoningPower}, time_spent = #{timeSpent}, " +
            "attempts_count = #{attemptsCount}, updated_at = NOW() WHERE completion_id = #{completionId}")
    int update(UserDailyPuzzleCompletion completion);

    /**
     * 软删除完成记录
     */
    @Update("UPDATE user_daily_puzzle_completions SET deleted = true, updated_at = NOW() WHERE completion_id = #{completionId}")
    int deleteById(Long completionId);

    /**
     * 统计用户完成的每日一推数量
     */
    @Select("SELECT COUNT(*) FROM user_daily_puzzle_completions WHERE user_id = #{userId} AND deleted = false")
    int countByUserId(Long userId);

    /**
     * 统计用户本月完成的每日一推数量
     */
    @Select("SELECT COUNT(*) FROM user_daily_puzzle_completions WHERE user_id = #{userId} " +
            "AND YEAR(completion_date) = YEAR(#{month}) AND MONTH(completion_date) = MONTH(#{month}) " +
            "AND deleted = false")
    int countByUserIdAndMonth(@Param("userId") Long userId, @Param("month") LocalDate month);

    /**
     * 统计指定每日一推的完成人数
     */
    @Select("SELECT COUNT(*) FROM user_daily_puzzle_completions WHERE daily_id = #{dailyId} AND deleted = false")
    int countByDailyId(Long dailyId);

    /**
     * 获取用户连续完成每日一推的天数（简化版本）
     */
    @Select("SELECT COUNT(*) FROM user_daily_puzzle_completions " +
            "WHERE user_id = #{userId} AND deleted = false " +
            "AND completion_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)")
    int getConsecutiveDays(Long userId);
}
