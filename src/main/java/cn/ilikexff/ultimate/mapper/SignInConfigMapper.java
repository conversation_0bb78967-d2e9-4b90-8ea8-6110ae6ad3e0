package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.SignInConfig;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 签到配置Mapper接口
 */
@Mapper
public interface SignInConfigMapper {

    /**
     * 根据ID查询配置
     */
    @Select("SELECT * FROM sign_in_config WHERE config_id = #{configId} AND deleted = false")
    SignInConfig findById(Long configId);

    /**
     * 根据天数查询配置
     */
    @Select("SELECT * FROM sign_in_config WHERE day_number = #{dayNumber} AND is_active = true AND deleted = false")
    SignInConfig findByDayNumber(Integer dayNumber);

    /**
     * 查询所有启用的配置
     */
    @Select("SELECT * FROM sign_in_config WHERE is_active = true AND deleted = false ORDER BY day_number")
    List<SignInConfig> findAllActive();

    /**
     * 查询所有配置
     */
    @Select("SELECT * FROM sign_in_config WHERE deleted = false ORDER BY day_number")
    List<SignInConfig> findAll();

    /**
     * 插入配置
     */
    @Insert("INSERT INTO sign_in_config (day_number, base_reward, bonus_reward, reward_type, " +
            "description, is_active, created_at, updated_at, deleted) " +
            "VALUES (#{dayNumber}, #{baseReward}, #{bonusReward}, #{rewardType}, " +
            "#{description}, #{isActive}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "configId")
    int insert(SignInConfig config);

    /**
     * 更新配置
     */
    @Update("UPDATE sign_in_config SET day_number = #{dayNumber}, base_reward = #{baseReward}, " +
            "bonus_reward = #{bonusReward}, reward_type = #{rewardType}, description = #{description}, " +
            "is_active = #{isActive}, updated_at = NOW() WHERE config_id = #{configId}")
    int update(SignInConfig config);

    /**
     * 软删除配置
     */
    @Update("UPDATE sign_in_config SET deleted = true, updated_at = NOW() WHERE config_id = #{configId}")
    int deleteById(Long configId);

    /**
     * 启用/禁用配置
     */
    @Update("UPDATE sign_in_config SET is_active = #{isActive}, updated_at = NOW() WHERE config_id = #{configId}")
    int updateActiveStatus(@Param("configId") Long configId, @Param("isActive") Boolean isActive);
}
