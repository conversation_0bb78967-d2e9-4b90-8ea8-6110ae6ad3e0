package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.UserSignIn;
import org.apache.ibatis.annotations.*;
import java.time.LocalDate;
import java.util.List;

/**
 * 用户签到记录Mapper接口
 */
@Mapper
public interface UserSignInMapper {

    /**
     * 根据ID查询签到记录
     */
    @Select("SELECT * FROM user_sign_in WHERE sign_in_id = #{signInId} AND deleted = false")
    UserSignIn findById(Long signInId);

    /**
     * 根据用户ID和日期查询签到记录
     */
    @Select("SELECT * FROM user_sign_in WHERE user_id = #{userId} AND sign_in_date = #{signInDate} AND deleted = false")
    UserSignIn findByUserIdAndDate(@Param("userId") Long userId, @Param("signInDate") LocalDate signInDate);

    /**
     * 根据用户ID查询所有签到记录
     */
    @Select("SELECT * FROM user_sign_in WHERE user_id = #{userId} AND deleted = false ORDER BY sign_in_date DESC")
    List<UserSignIn> findByUserId(Long userId);

    /**
     * 根据用户ID查询最近的签到记录
     */
    @Select("SELECT * FROM user_sign_in WHERE user_id = #{userId} AND deleted = false ORDER BY sign_in_date DESC LIMIT 1")
    UserSignIn findLatestByUserId(Long userId);

    /**
     * 根据用户ID查询指定日期范围内的签到记录
     */
    @Select("SELECT * FROM user_sign_in WHERE user_id = #{userId} AND sign_in_date BETWEEN #{startDate} AND #{endDate} AND deleted = false ORDER BY sign_in_date DESC")
    List<UserSignIn> findByUserIdAndDateRange(@Param("userId") Long userId, 
                                             @Param("startDate") LocalDate startDate, 
                                             @Param("endDate") LocalDate endDate);

    /**
     * 检查用户今天是否已签到
     */
    @Select("SELECT COUNT(*) > 0 FROM user_sign_in WHERE user_id = #{userId} AND sign_in_date = #{today} AND deleted = false")
    boolean hasSignedInToday(@Param("userId") Long userId, @Param("today") LocalDate today);

    /**
     * 插入签到记录
     */
    @Insert("INSERT INTO user_sign_in (user_id, sign_in_date, reward_u_coins, consecutive_days, " +
            "is_double_reward, reward_type, created_at, updated_at, deleted) " +
            "VALUES (#{userId}, #{signInDate}, #{rewardUCoins}, #{consecutiveDays}, " +
            "#{isDoubleReward}, #{rewardType}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "signInId")
    int insert(UserSignIn signIn);

    /**
     * 更新签到记录
     */
    @Update("UPDATE user_sign_in SET user_id = #{userId}, sign_in_date = #{signInDate}, " +
            "reward_u_coins = #{rewardUCoins}, consecutive_days = #{consecutiveDays}, " +
            "is_double_reward = #{isDoubleReward}, reward_type = #{rewardType}, " +
            "updated_at = NOW() WHERE sign_in_id = #{signInId}")
    int update(UserSignIn signIn);

    /**
     * 软删除签到记录
     */
    @Update("UPDATE user_sign_in SET deleted = true, updated_at = NOW() WHERE sign_in_id = #{signInId}")
    int deleteById(Long signInId);

    /**
     * 统计用户总签到天数
     */
    @Select("SELECT COUNT(*) FROM user_sign_in WHERE user_id = #{userId} AND deleted = false")
    int countTotalSignInDays(Long userId);

    /**
     * 统计用户当月签到天数
     */
    @Select("SELECT COUNT(*) FROM user_sign_in WHERE user_id = #{userId} " +
            "AND YEAR(sign_in_date) = YEAR(#{month}) AND MONTH(sign_in_date) = MONTH(#{month}) " +
            "AND deleted = false")
    int countMonthlySignInDays(@Param("userId") Long userId, @Param("month") LocalDate month);

    /**
     * 获取用户最大连续签到天数
     */
    @Select("SELECT MAX(consecutive_days) FROM user_sign_in WHERE user_id = #{userId} AND deleted = false")
    Integer getMaxConsecutiveDays(Long userId);

    /**
     * 获取用户当前连续签到天数
     */
    @Select("SELECT consecutive_days FROM user_sign_in WHERE user_id = #{userId} AND deleted = false ORDER BY sign_in_date DESC LIMIT 1")
    Integer getCurrentConsecutiveDays(Long userId);

    /**
     * 获取用户本月签到记录（用于签到日历显示）
     */
    @Select("SELECT sign_in_date FROM user_sign_in WHERE user_id = #{userId} " +
            "AND YEAR(sign_in_date) = YEAR(#{month}) AND MONTH(sign_in_date) = MONTH(#{month}) " +
            "AND deleted = false ORDER BY sign_in_date")
    List<LocalDate> getMonthlySignInDates(@Param("userId") Long userId, @Param("month") LocalDate month);
}
