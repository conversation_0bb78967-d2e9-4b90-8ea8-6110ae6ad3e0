package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.UserPuzzleProgress;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户谜题进度Mapper接口
 */
@Mapper
public interface UserPuzzleProgressMapper {

    /**
     * 根据用户ID和谜题ID查询进度
     */
    @Select("SELECT * FROM user_puzzle_progress WHERE user_id = #{userId} AND puzzle_id = #{puzzleId} AND deleted = false")
    UserPuzzleProgress findByUserIdAndPuzzleId(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId);

    /**
     * 根据用户ID查询所有进度
     */
    @Select("SELECT * FROM user_puzzle_progress WHERE user_id = #{userId} AND deleted = false ORDER BY created_at DESC")
    List<UserPuzzleProgress> findByUserId(Long userId);

    /**
     * 根据谜题ID查询所有进度
     */
    @Select("SELECT * FROM user_puzzle_progress WHERE puzzle_id = #{puzzleId} AND deleted = false")
    List<UserPuzzleProgress> findByPuzzleId(Long puzzleId);

    /**
     * 查询用户已完成的谜题进度
     */
    @Select("SELECT * FROM user_puzzle_progress WHERE user_id = #{userId} AND status = 'COMPLETED' AND deleted = false ORDER BY completion_date DESC")
    List<UserPuzzleProgress> findCompletedByUserId(Long userId);

    /**
     * 查询用户进行中的谜题进度
     */
    @Select("SELECT * FROM user_puzzle_progress WHERE user_id = #{userId} AND status = 'IN_PROGRESS' AND deleted = false ORDER BY updated_at DESC")
    List<UserPuzzleProgress> findInProgressByUserId(Long userId);

    /**
     * 插入用户谜题进度
     */
    @Insert("INSERT INTO user_puzzle_progress (user_id, puzzle_id, status, attempts_count, " +
            "start_time, earned_u_coins, earned_reasoning_power, total_time_spent, hints_used, " +
            "created_at, updated_at, deleted) " +
            "VALUES (#{userId}, #{puzzleId}, #{status}, #{attemptsCount}, #{startTime}, " +
            "#{earnedUCoins}, #{earnedReasoningPower}, #{totalTimeSpent}, #{hintsUsed}, " +
            "#{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "progressId")
    int insert(UserPuzzleProgress progress);

    /**
     * 更新用户谜题进度
     */
    @Update("UPDATE user_puzzle_progress SET status = #{status}, attempts_count = #{attemptsCount}, " +
            "completion_date = #{completionDate}, earned_u_coins = #{earnedUCoins}, " +
            "earned_reasoning_power = #{earnedReasoningPower}, total_time_spent = #{totalTimeSpent}, " +
            "hints_used = #{hintsUsed}, rating = #{rating}, updated_at = NOW() " +
            "WHERE progress_id = #{progressId}")
    int update(UserPuzzleProgress progress);

    /**
     * 增加尝试次数
     */
    @Update("UPDATE user_puzzle_progress SET attempts_count = attempts_count + 1, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND puzzle_id = #{puzzleId}")
    int incrementAttemptCount(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId);

    /**
     * 更新进度状态
     */
    @Update("UPDATE user_puzzle_progress SET status = #{status}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND puzzle_id = #{puzzleId}")
    int updateStatus(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId, @Param("status") String status);

    /**
     * 完成谜题
     */
    @Update("UPDATE user_puzzle_progress SET status = 'COMPLETED', completion_date = NOW(), " +
            "earned_u_coins = #{earnedUCoins}, earned_reasoning_power = #{earnedReasoningPower}, " +
            "total_time_spent = #{totalTimeSpent}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND puzzle_id = #{puzzleId}")
    int completePuzzle(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId, 
                      @Param("earnedUCoins") Integer earnedUCoins, 
                      @Param("earnedReasoningPower") Integer earnedReasoningPower,
                      @Param("totalTimeSpent") Long totalTimeSpent);

    /**
     * 增加使用的提示数量
     */
    @Update("UPDATE user_puzzle_progress SET hints_used = hints_used + 1, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND puzzle_id = #{puzzleId}")
    int incrementHintsUsed(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId);

    /**
     * 更新用户评分
     */
    @Update("UPDATE user_puzzle_progress SET rating = #{rating}, updated_at = NOW() " +
            "WHERE user_id = #{userId} AND puzzle_id = #{puzzleId}")
    int updateRating(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId, @Param("rating") Integer rating);

    /**
     * 统计用户完成的谜题数量
     */
    @Select("SELECT COUNT(*) FROM user_puzzle_progress WHERE user_id = #{userId} AND status = 'COMPLETED' AND deleted = false")
    int countCompletedByUserId(Long userId);

    /**
     * 统计用户进行中的谜题数量
     */
    @Select("SELECT COUNT(*) FROM user_puzzle_progress WHERE user_id = #{userId} AND status = 'IN_PROGRESS' AND deleted = false")
    int countInProgressByUserId(Long userId);

    /**
     * 统计谜题的完成人数
     */
    @Select("SELECT COUNT(*) FROM user_puzzle_progress WHERE puzzle_id = #{puzzleId} AND status = 'COMPLETED' AND deleted = false")
    int countCompletedByPuzzleId(Long puzzleId);

    /**
     * 获取谜题的平均评分
     */
    @Select("SELECT AVG(rating) FROM user_puzzle_progress WHERE puzzle_id = #{puzzleId} AND rating IS NOT NULL AND deleted = false")
    Double getAverageRatingByPuzzleId(Long puzzleId);

    /**
     * 软删除进度记录
     */
    @Update("UPDATE user_puzzle_progress SET deleted = true, updated_at = NOW() WHERE progress_id = #{progressId}")
    int deleteById(Long progressId);
}
