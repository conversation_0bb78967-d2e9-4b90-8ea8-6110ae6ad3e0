package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.User;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper {

    /**
     * 根据ID查询用户
     */
    @Select("SELECT * FROM users WHERE user_id = #{userId} AND deleted = false")
    User findById(Long userId);

    /**
     * 根据用户名查询用户
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = false")
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = false")
    User findByEmail(String email);

    /**
     * 根据手机号查询用户
     */
    @Select("SELECT * FROM users WHERE phone_number = #{phoneNumber} AND deleted = false")
    User findByPhoneNumber(String phoneNumber);

    /**
     * 根据QQ OpenID查询用户
     */
    @Select("SELECT * FROM users WHERE qq_openid = #{qqOpenid} AND deleted = false")
    User findByQqOpenid(String qqOpenid);

    /**
     * 插入用户
     */
    @Insert("INSERT INTO users (username, password_hash, email, phone_number, qq_openid, nickname, " +
            "avatar_url, u_coins, reasoning_power, registration_date, status, created_at, updated_at) " +
            "VALUES (#{username}, #{passwordHash}, #{email}, #{phoneNumber}, #{qqOpenid}, #{nickname}, " +
            "#{avatarUrl}, #{uCoins}, #{reasoningPower}, #{registrationDate}, #{status}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "userId")
    int insert(User user);

    /**
     * 更新用户信息
     */
    @Update("UPDATE users SET nickname = #{nickname}, avatar_url = #{avatarUrl}, " +
            "email = #{email}, phone_number = #{phoneNumber}, current_title_id = #{currentTitleId}, " +
            "updated_at = NOW() WHERE user_id = #{userId}")
    int updateUserInfo(User user);

    /**
     * 更新用户U币
     */
    @Update("UPDATE users SET u_coins = #{uCoins}, updated_at = NOW() WHERE user_id = #{userId}")
    int updateUCoins(@Param("userId") Long userId, @Param("uCoins") Integer uCoins);

    /**
     * 更新用户推理力
     */
    @Update("UPDATE users SET reasoning_power = #{reasoningPower}, updated_at = NOW() WHERE user_id = #{userId}")
    int updateReasoningPower(@Param("userId") Long userId, @Param("reasoningPower") Integer reasoningPower);

    /**
     * 更新登录信息
     */
    @Update("UPDATE users SET last_login_time = NOW(), login_count = login_count + 1, updated_at = NOW() " +
            "WHERE user_id = #{userId}")
    int updateLoginInfo(Long userId);

    /**
     * 更新密码
     */
    @Update("UPDATE users SET password_hash = #{passwordHash}, updated_at = NOW() WHERE user_id = #{userId}")
    int updatePassword(@Param("userId") Long userId, @Param("passwordHash") String passwordHash);

    /**
     * 更新用户当前称号
     */
    @Update("UPDATE users SET current_title_id = #{titleId}, updated_at = NOW() WHERE user_id = #{userId}")
    int updateCurrentTitle(@Param("userId") Long userId, @Param("titleId") Long titleId);

    /**
     * 增加用户U币
     */
    @Update("UPDATE users SET u_coins = u_coins + #{coins}, updated_at = NOW() WHERE user_id = #{userId}")
    int addUCoins(@Param("userId") Long userId, @Param("coins") Integer coins);

    /**
     * 增加用户推理力
     */
    @Update("UPDATE users SET reasoning_power = reasoning_power + #{reasoningPower}, updated_at = NOW() WHERE user_id = #{userId}")
    int addReasoningPower(@Param("userId") Long userId, @Param("reasoningPower") Integer reasoningPower);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username} AND deleted = false")
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email} AND deleted = false")
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE phone_number = #{phoneNumber} AND deleted = false")
    boolean existsByPhoneNumber(String phoneNumber);

    /**
     * 根据推理力排序查询用户排行榜
     */
    @Select("SELECT user_id, username, nickname, avatar_url, reasoning_power " +
            "FROM users WHERE deleted = false AND status = 'ACTIVE' " +
            "ORDER BY reasoning_power DESC LIMIT #{limit}")
    List<User> findTopUsersByReasoningPower(int limit);

    /**
     * 获取用户推理力排名
     */
    @Select("SELECT COUNT(*) + 1 FROM users " +
            "WHERE reasoning_power > (SELECT reasoning_power FROM users WHERE user_id = #{userId}) " +
            "AND deleted = false AND status = 'ACTIVE'")
    Integer getUserRankByReasoningPower(Long userId);

    /**
     * 软删除用户
     */
    @Update("UPDATE users SET deleted = true, updated_at = NOW() WHERE user_id = #{userId}")
    int deleteById(Long userId);
}
