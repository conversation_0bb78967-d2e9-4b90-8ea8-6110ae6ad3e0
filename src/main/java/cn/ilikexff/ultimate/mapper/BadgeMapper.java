package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.Badge;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 勋章Mapper接口
 */
@Mapper
public interface BadgeMapper {

    /**
     * 根据ID查询勋章
     */
    @Select("SELECT * FROM badges WHERE badge_id = #{badgeId} AND deleted = false")
    Badge findById(Long badgeId);

    /**
     * 查询所有勋章
     */
    @Select("SELECT * FROM badges WHERE deleted = false ORDER BY badge_id ASC")
    List<Badge> findAll();

    /**
     * 根据名称查询勋章
     */
    @Select("SELECT * FROM badges WHERE badge_name = #{badgeName} AND deleted = false")
    Badge findByName(String badgeName);

    /**
     * 插入勋章
     */
    @Insert("INSERT INTO badges (badge_name, icon_url, description, acquisition_condition, created_at, updated_at, deleted) " +
            "VALUES (#{badgeName}, #{iconUrl}, #{description}, #{acquisitionCondition}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "badgeId")
    int insert(Badge badge);

    /**
     * 更新勋章
     */
    @Update("UPDATE badges SET badge_name = #{badgeName}, icon_url = #{iconUrl}, " +
            "description = #{description}, acquisition_condition = #{acquisitionCondition}, " +
            "updated_at = NOW() WHERE badge_id = #{badgeId}")
    int update(Badge badge);

    /**
     * 软删除勋章
     */
    @Update("UPDATE badges SET deleted = true, updated_at = NOW() WHERE badge_id = #{badgeId}")
    int deleteById(Long badgeId);

    /**
     * 统计勋章数量
     */
    @Select("SELECT COUNT(*) FROM badges WHERE deleted = false")
    int count();
}
