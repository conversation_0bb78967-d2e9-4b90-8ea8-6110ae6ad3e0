package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.UserHintLog;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户提示记录Mapper接口
 */
@Mapper
public interface UserHintLogMapper {

    /**
     * 根据ID查询记录
     */
    @Select("SELECT * FROM user_hint_log WHERE log_id = #{logId} AND deleted = false")
    UserHintLog findById(Long logId);

    /**
     * 根据用户ID和提示ID查询记录
     */
    @Select("SELECT * FROM user_hint_log WHERE user_id = #{userId} AND hint_id = #{hintId} AND deleted = false")
    UserHintLog findByUserIdAndHintId(@Param("userId") Long userId, @Param("hintId") Long hintId);

    /**
     * 根据用户ID查询所有提示记录
     */
    @Select("SELECT * FROM user_hint_log WHERE user_id = #{userId} AND deleted = false ORDER BY purchase_time DESC")
    List<UserHintLog> findByUserId(Long userId);

    /**
     * 根据提示ID查询所有购买记录
     */
    @Select("SELECT * FROM user_hint_log WHERE hint_id = #{hintId} AND deleted = false ORDER BY purchase_time DESC")
    List<UserHintLog> findByHintId(Long hintId);

    /**
     * 根据用户ID和谜题ID查询已购买的提示
     */
    @Select("SELECT uhl.* FROM user_hint_log uhl " +
            "JOIN hints h ON uhl.hint_id = h.hint_id " +
            "WHERE uhl.user_id = #{userId} AND h.puzzle_id = #{puzzleId} AND uhl.deleted = false " +
            "ORDER BY uhl.purchase_time DESC")
    List<UserHintLog> findByUserIdAndPuzzleId(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId);

    /**
     * 检查用户是否已购买指定提示
     */
    @Select("SELECT COUNT(*) > 0 FROM user_hint_log WHERE user_id = #{userId} AND hint_id = #{hintId} AND deleted = false")
    boolean hasUserPurchasedHint(@Param("userId") Long userId, @Param("hintId") Long hintId);

    /**
     * 插入提示购买记录
     */
    @Insert("INSERT INTO user_hint_log (user_id, hint_id, purchase_time, created_at, updated_at, deleted) " +
            "VALUES (#{userId}, #{hintId}, #{purchaseTime}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "logId")
    int insert(UserHintLog log);

    /**
     * 更新记录
     */
    @Update("UPDATE user_hint_log SET user_id = #{userId}, hint_id = #{hintId}, " +
            "purchase_time = #{purchaseTime}, updated_at = NOW() WHERE log_id = #{logId}")
    int update(UserHintLog log);

    /**
     * 软删除记录
     */
    @Update("UPDATE user_hint_log SET deleted = true, updated_at = NOW() WHERE log_id = #{logId}")
    int deleteById(Long logId);

    /**
     * 统计用户购买的提示数量
     */
    @Select("SELECT COUNT(*) FROM user_hint_log WHERE user_id = #{userId} AND deleted = false")
    int countByUserId(Long userId);

    /**
     * 统计用户在指定谜题上购买的提示数量
     */
    @Select("SELECT COUNT(*) FROM user_hint_log uhl " +
            "JOIN hints h ON uhl.hint_id = h.hint_id " +
            "WHERE uhl.user_id = #{userId} AND h.puzzle_id = #{puzzleId} AND uhl.deleted = false")
    int countByUserIdAndPuzzleId(@Param("userId") Long userId, @Param("puzzleId") Long puzzleId);

    /**
     * 统计提示的购买次数
     */
    @Select("SELECT COUNT(*) FROM user_hint_log WHERE hint_id = #{hintId} AND deleted = false")
    int countByHintId(Long hintId);
}
