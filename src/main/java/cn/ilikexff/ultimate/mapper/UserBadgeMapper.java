package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.UserBadge;
import cn.ilikexff.ultimate.entity.Badge;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户勋章关联Mapper接口
 */
@Mapper
public interface UserBadgeMapper {

    /**
     * 根据ID查询用户勋章关联
     */
    @Select("SELECT * FROM user_badges WHERE id = #{id} AND deleted = false")
    UserBadge findById(Long id);

    /**
     * 根据用户ID和勋章ID查询关联
     */
    @Select("SELECT * FROM user_badges WHERE user_id = #{userId} AND badge_id = #{badgeId} AND deleted = false")
    UserBadge findByUserIdAndBadgeId(@Param("userId") Long userId, @Param("badgeId") Long badgeId);

    /**
     * 根据用户ID查询所有勋章关联
     */
    @Select("SELECT * FROM user_badges WHERE user_id = #{userId} AND deleted = false ORDER BY acquisition_date DESC")
    List<UserBadge> findByUserId(Long userId);

    /**
     * 根据用户ID查询所有勋章详情
     */
    @Select("SELECT b.* FROM user_badges ub " +
            "JOIN badges b ON ub.badge_id = b.badge_id " +
            "WHERE ub.user_id = #{userId} AND ub.deleted = false AND b.deleted = false " +
            "ORDER BY ub.acquisition_date DESC")
    List<Badge> findBadgesByUserId(Long userId);

    /**
     * 检查用户是否拥有指定勋章
     */
    @Select("SELECT COUNT(*) > 0 FROM user_badges WHERE user_id = #{userId} AND badge_id = #{badgeId} AND deleted = false")
    boolean hasUserBadge(@Param("userId") Long userId, @Param("badgeId") Long badgeId);

    /**
     * 插入用户勋章关联
     */
    @Insert("INSERT INTO user_badges (user_id, badge_id, acquisition_date, created_at, updated_at, deleted) " +
            "VALUES (#{userId}, #{badgeId}, #{acquisitionDate}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserBadge userBadge);

    /**
     * 更新用户勋章关联
     */
    @Update("UPDATE user_badges SET user_id = #{userId}, badge_id = #{badgeId}, " +
            "acquisition_date = #{acquisitionDate}, updated_at = NOW() WHERE id = #{id}")
    int update(UserBadge userBadge);

    /**
     * 软删除用户勋章关联
     */
    @Update("UPDATE user_badges SET deleted = true, updated_at = NOW() WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 删除用户的指定勋章
     */
    @Update("UPDATE user_badges SET deleted = true, updated_at = NOW() WHERE user_id = #{userId} AND badge_id = #{badgeId}")
    int deleteByUserIdAndBadgeId(@Param("userId") Long userId, @Param("badgeId") Long badgeId);

    /**
     * 统计用户拥有的勋章数量
     */
    @Select("SELECT COUNT(*) FROM user_badges WHERE user_id = #{userId} AND deleted = false")
    int countByUserId(Long userId);

    /**
     * 统计拥有指定勋章的用户数量
     */
    @Select("SELECT COUNT(*) FROM user_badges WHERE badge_id = #{badgeId} AND deleted = false")
    int countByBadgeId(Long badgeId);
}
