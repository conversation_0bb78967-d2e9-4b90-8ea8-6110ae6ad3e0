package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.UserTitle;
import cn.ilikexff.ultimate.entity.Title;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 用户称号关联Mapper接口
 */
@Mapper
public interface UserTitleMapper {

    /**
     * 根据ID查询用户称号关联
     */
    @Select("SELECT * FROM user_titles WHERE id = #{id} AND deleted = false")
    UserTitle findById(Long id);

    /**
     * 根据用户ID和称号ID查询关联
     */
    @Select("SELECT * FROM user_titles WHERE user_id = #{userId} AND title_id = #{titleId} AND deleted = false")
    UserTitle findByUserIdAndTitleId(@Param("userId") Long userId, @Param("titleId") Long titleId);

    /**
     * 根据用户ID查询所有称号关联
     */
    @Select("SELECT * FROM user_titles WHERE user_id = #{userId} AND deleted = false ORDER BY acquisition_date DESC")
    List<UserTitle> findByUserId(Long userId);

    /**
     * 根据用户ID查询所有称号详情
     */
    @Select("SELECT t.* FROM user_titles ut " +
            "JOIN titles t ON ut.title_id = t.title_id " +
            "WHERE ut.user_id = #{userId} AND ut.deleted = false AND t.deleted = false " +
            "ORDER BY ut.acquisition_date DESC")
    List<Title> findTitlesByUserId(Long userId);

    /**
     * 检查用户是否拥有指定称号
     */
    @Select("SELECT COUNT(*) > 0 FROM user_titles WHERE user_id = #{userId} AND title_id = #{titleId} AND deleted = false")
    boolean hasUserTitle(@Param("userId") Long userId, @Param("titleId") Long titleId);

    /**
     * 插入用户称号关联
     */
    @Insert("INSERT INTO user_titles (user_id, title_id, acquisition_date, created_at, updated_at, deleted) " +
            "VALUES (#{userId}, #{titleId}, #{acquisitionDate}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserTitle userTitle);

    /**
     * 更新用户称号关联
     */
    @Update("UPDATE user_titles SET user_id = #{userId}, title_id = #{titleId}, " +
            "acquisition_date = #{acquisitionDate}, updated_at = NOW() WHERE id = #{id}")
    int update(UserTitle userTitle);

    /**
     * 软删除用户称号关联
     */
    @Update("UPDATE user_titles SET deleted = true, updated_at = NOW() WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 删除用户的指定称号
     */
    @Update("UPDATE user_titles SET deleted = true, updated_at = NOW() WHERE user_id = #{userId} AND title_id = #{titleId}")
    int deleteByUserIdAndTitleId(@Param("userId") Long userId, @Param("titleId") Long titleId);

    /**
     * 统计用户拥有的称号数量
     */
    @Select("SELECT COUNT(*) FROM user_titles WHERE user_id = #{userId} AND deleted = false")
    int countByUserId(Long userId);

    /**
     * 统计拥有指定称号的用户数量
     */
    @Select("SELECT COUNT(*) FROM user_titles WHERE title_id = #{titleId} AND deleted = false")
    int countByTitleId(Long titleId);
}
