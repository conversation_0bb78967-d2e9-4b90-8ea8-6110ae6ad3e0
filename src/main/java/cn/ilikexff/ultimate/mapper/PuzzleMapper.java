package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.Puzzle;
import org.apache.ibatis.annotations.*;
import java.time.LocalDate;
import java.util.List;

/**
 * 谜题Mapper接口
 */
@Mapper
public interface PuzzleMapper {

    /**
     * 根据ID查询谜题
     */
    @Select("SELECT * FROM puzzles WHERE puzzle_id = #{puzzleId} AND deleted = false")
    Puzzle findById(Long puzzleId);

    /**
     * 查询所有已发布的谜题
     */
    @Select("SELECT * FROM puzzles WHERE status = 'PUBLISHED' AND deleted = false ORDER BY created_at DESC")
    List<Puzzle> findAll();

    /**
     * 查询已发布的谜题列表
     */
    @Select("SELECT * FROM puzzles WHERE status = 'PUBLISHED' AND deleted = false " +
            "ORDER BY created_at DESC LIMIT #{offset}, #{limit}")
    List<Puzzle> findPublishedPuzzles(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据难度查询谜题
     */
    @Select("SELECT * FROM puzzles WHERE difficulty = #{difficulty} AND status = 'PUBLISHED' AND deleted = false " +
            "ORDER BY created_at DESC LIMIT #{offset}, #{limit}")
    List<Puzzle> findByDifficulty(@Param("difficulty") String difficulty,
                                  @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据类型查询谜题
     */
    @Select("SELECT * FROM puzzles WHERE puzzle_type = #{puzzleType} AND status = 'PUBLISHED' AND deleted = false " +
            "ORDER BY created_at DESC LIMIT #{offset}, #{limit}")
    List<Puzzle> findByType(@Param("puzzleType") String puzzleType,
                           @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 查询每日一推
     */
    @Select("SELECT * FROM puzzles WHERE is_daily = true AND daily_date = #{date} " +
            "AND status = 'PUBLISHED' AND deleted = false")
    Puzzle findDailyPuzzle(LocalDate date);

    /**
     * 查询最新的每日一推
     */
    @Select("SELECT * FROM puzzles WHERE is_daily = true AND status = 'PUBLISHED' AND deleted = false " +
            "ORDER BY daily_date DESC LIMIT 1")
    Puzzle findLatestDailyPuzzle();

    /**
     * 插入谜题
     */
    @Insert("INSERT INTO puzzles (puzzle_name, description, background_story, clues, difficulty, " +
            "required_u_coins, u_coins_reward, reasoning_power_reward, answer, puzzle_type, " +
            "created_by, status, is_daily, daily_date, created_at, updated_at) " +
            "VALUES (#{puzzleName}, #{description}, #{backgroundStory}, #{clues}, #{difficulty}, " +
            "#{requiredUCoins}, #{uCoinsReward}, #{reasoningPowerReward}, #{answer}, #{puzzleType}, " +
            "#{createdBy}, #{status}, #{isDaily}, #{dailyDate}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "puzzleId")
    int insert(Puzzle puzzle);

    /**
     * 更新谜题信息
     */
    @Update("UPDATE puzzles SET puzzle_name = #{puzzleName}, description = #{description}, " +
            "background_story = #{backgroundStory}, clues = #{clues}, difficulty = #{difficulty}, " +
            "required_u_coins = #{requiredUCoins}, u_coins_reward = #{uCoinsReward}, " +
            "reasoning_power_reward = #{reasoningPowerReward}, answer = #{answer}, " +
            "puzzle_type = #{puzzleType}, status = #{status}, updated_at = NOW() " +
            "WHERE puzzle_id = #{puzzleId}")
    int update(Puzzle puzzle);

    /**
     * 增加游玩次数
     */
    @Update("UPDATE puzzles SET play_count = play_count + 1, updated_at = NOW() WHERE puzzle_id = #{puzzleId}")
    int incrementPlayCount(Long puzzleId);

    /**
     * 增加完成次数
     */
    @Update("UPDATE puzzles SET completion_count = completion_count + 1, updated_at = NOW() WHERE puzzle_id = #{puzzleId}")
    int incrementCompletionCount(Long puzzleId);

    /**
     * 更新平均评分
     */
    @Update("UPDATE puzzles SET average_rating = #{averageRating}, updated_at = NOW() WHERE puzzle_id = #{puzzleId}")
    int updateAverageRating(@Param("puzzleId") Long puzzleId, @Param("averageRating") Double averageRating);

    /**
     * 统计已发布谜题总数
     */
    @Select("SELECT COUNT(*) FROM puzzles WHERE status = 'PUBLISHED' AND deleted = false")
    int countPublishedPuzzles();

    /**
     * 根据难度统计谜题数量
     */
    @Select("SELECT COUNT(*) FROM puzzles WHERE difficulty = #{difficulty} AND status = 'PUBLISHED' AND deleted = false")
    int countByDifficulty(String difficulty);

    /**
     * 软删除谜题
     */
    @Update("UPDATE puzzles SET deleted = true, updated_at = NOW() WHERE puzzle_id = #{puzzleId}")
    int deleteById(Long puzzleId);
}
