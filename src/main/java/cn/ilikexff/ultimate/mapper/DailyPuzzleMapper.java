package cn.ilikexff.ultimate.mapper;

import cn.ilikexff.ultimate.entity.DailyPuzzle;
import org.apache.ibatis.annotations.*;
import java.time.LocalDate;
import java.util.List;

/**
 * 每日一推Mapper接口
 */
@Mapper
public interface DailyPuzzleMapper {

    /**
     * 根据ID查询每日一推
     */
    @Select("SELECT * FROM daily_puzzles WHERE daily_id = #{dailyId} AND deleted = false")
    DailyPuzzle findById(Long dailyId);

    /**
     * 根据日期查询每日一推
     */
    @Select("SELECT * FROM daily_puzzles WHERE puzzle_date = #{puzzleDate} AND is_active = true AND deleted = false")
    DailyPuzzle findByDate(LocalDate puzzleDate);

    /**
     * 获取今日一推
     */
    @Select("SELECT * FROM daily_puzzles WHERE puzzle_date = CURDATE() AND is_active = true AND deleted = false")
    DailyPuzzle findToday();

    /**
     * 根据谜题ID查询每日一推记录
     */
    @Select("SELECT * FROM daily_puzzles WHERE puzzle_id = #{puzzleId} AND deleted = false ORDER BY puzzle_date DESC")
    List<DailyPuzzle> findByPuzzleId(Long puzzleId);

    /**
     * 查询指定日期范围内的每日一推
     */
    @Select("SELECT * FROM daily_puzzles WHERE puzzle_date BETWEEN #{startDate} AND #{endDate} " +
            "AND is_active = true AND deleted = false ORDER BY puzzle_date DESC")
    List<DailyPuzzle> findByDateRange(@Param("startDate") LocalDate startDate, 
                                     @Param("endDate") LocalDate endDate);

    /**
     * 查询最近的每日一推
     */
    @Select("SELECT * FROM daily_puzzles WHERE is_active = true AND deleted = false " +
            "ORDER BY puzzle_date DESC LIMIT #{limit}")
    List<DailyPuzzle> findRecent(int limit);

    /**
     * 插入每日一推
     */
    @Insert("INSERT INTO daily_puzzles (puzzle_date, puzzle_id, theme, description, " +
            "bonus_u_coins, bonus_reasoning_power, is_active, created_at, updated_at, deleted) " +
            "VALUES (#{puzzleDate}, #{puzzleId}, #{theme}, #{description}, " +
            "#{bonusUCoins}, #{bonusReasoningPower}, #{isActive}, #{createdAt}, #{updatedAt}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "dailyId")
    int insert(DailyPuzzle dailyPuzzle);

    /**
     * 更新每日一推
     */
    @Update("UPDATE daily_puzzles SET puzzle_date = #{puzzleDate}, puzzle_id = #{puzzleId}, " +
            "theme = #{theme}, description = #{description}, bonus_u_coins = #{bonusUCoins}, " +
            "bonus_reasoning_power = #{bonusReasoningPower}, is_active = #{isActive}, " +
            "updated_at = NOW() WHERE daily_id = #{dailyId}")
    int update(DailyPuzzle dailyPuzzle);

    /**
     * 软删除每日一推
     */
    @Update("UPDATE daily_puzzles SET deleted = true, updated_at = NOW() WHERE daily_id = #{dailyId}")
    int deleteById(Long dailyId);

    /**
     * 启用/禁用每日一推
     */
    @Update("UPDATE daily_puzzles SET is_active = #{isActive}, updated_at = NOW() WHERE daily_id = #{dailyId}")
    int updateActiveStatus(@Param("dailyId") Long dailyId, @Param("isActive") Boolean isActive);

    /**
     * 检查指定日期是否已有每日一推
     */
    @Select("SELECT COUNT(*) > 0 FROM daily_puzzles WHERE puzzle_date = #{puzzleDate} AND deleted = false")
    boolean existsByDate(LocalDate puzzleDate);

    /**
     * 统计每日一推数量
     */
    @Select("SELECT COUNT(*) FROM daily_puzzles WHERE is_active = true AND deleted = false")
    int countActive();
}
