package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.dto.PuzzleListResponse;
import cn.ilikexff.ultimate.entity.Puzzle;
import cn.ilikexff.ultimate.entity.UserPuzzleProgress;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.PuzzleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 谜题服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PuzzleService {

    private final PuzzleMapper puzzleMapper;
    private final UserService userService;
    private final UserPuzzleProgressService progressService;
    private final HintService hintService;

    /**
     * 获取谜题列表
     */
    public PuzzleListResponse getPuzzleList(int page, int size, String difficulty, Long userId) {
        int offset = (page - 1) * size;

        List<Puzzle> puzzles;
        int total;

        if (difficulty != null && !difficulty.isEmpty()) {
            puzzles = puzzleMapper.findByDifficulty(difficulty, offset, size);
            total = puzzleMapper.countByDifficulty(difficulty);
        } else {
            puzzles = puzzleMapper.findPublishedPuzzles(offset, size);
            total = puzzleMapper.countPublishedPuzzles();
        }

        PuzzleListResponse response = new PuzzleListResponse();
        response.setPuzzles(convertToPuzzleInfoList(puzzles, userId));
        response.setTotal(total);
        response.setPage(page);
        response.setSize(size);
        response.setTotalPages((int) Math.ceil((double) total / size));

        return response;
    }

    /**
     * 根据ID获取谜题详情
     */
    public Puzzle getPuzzleById(Long puzzleId) {
        Puzzle puzzle = puzzleMapper.findById(puzzleId);
        if (puzzle == null) {
            throw new BusinessException("谜题不存在");
        }
        return puzzle;
    }

    /**
     * 获取今日推荐谜题
     */
    public Puzzle getDailyPuzzle() {
        Puzzle dailyPuzzle = puzzleMapper.findDailyPuzzle(LocalDate.now());
        if (dailyPuzzle == null) {
            throw new BusinessException("今日暂无推荐谜题");
        }
        return dailyPuzzle;
    }

    /**
     * 开始解谜
     */
    @Transactional
    public void startPuzzle(Long puzzleId, Long userId) {
        Puzzle puzzle = getPuzzleById(puzzleId);

        // 检查用户U币是否足够
        if (puzzle.getRequiredUCoins() > 0) {
            var user = userService.getUserById(userId);
            if (user.getUCoins() < puzzle.getRequiredUCoins()) {
                throw new BusinessException("U币余额不足");
            }

            // 扣除U币
            userService.deductUserCoins(userId, puzzle.getRequiredUCoins());
        }

        // 创建或更新用户谜题进度记录
        progressService.startPuzzle(userId, puzzleId);

        // 增加游玩次数
        puzzleMapper.incrementPlayCount(puzzleId);

        log.info("User {} started puzzle {}", userId, puzzleId);
    }

    /**
     * 提交答案
     */
    @Transactional
    public boolean submitAnswer(Long puzzleId, Long userId, String answer) {
        Puzzle puzzle = getPuzzleById(puzzleId);

        // 记录尝试次数
        progressService.recordAttempt(userId, puzzleId);

        // 检查答案是否正确
        boolean isCorrect = puzzle.getAnswer().trim().equalsIgnoreCase(answer.trim());

        if (isCorrect) {
            // 答案正确，给予奖励
            if (puzzle.getUCoinsReward() > 0) {
                userService.addUserCoins(userId, puzzle.getUCoinsReward());
            }
            if (puzzle.getReasoningPowerReward() > 0) {
                userService.addUserReasoningPower(userId, puzzle.getReasoningPowerReward());
            }

            // 计算花费时间并更新用户谜题进度为已完成
            Long timeSpent = progressService.calculateTimeSpent(userId, puzzleId);
            progressService.completePuzzle(userId, puzzleId, puzzle.getUCoinsReward(),
                                         puzzle.getReasoningPowerReward(), timeSpent);

            // 增加完成次数
            puzzleMapper.incrementCompletionCount(puzzleId);

            log.info("User {} completed puzzle {} successfully", userId, puzzleId);
        }

        return isCorrect;
    }

    /**
     * 获取谜题提示信息
     */
    public Object getPuzzleHintInfo(Long puzzleId, String hintLevel, Long userId) {
        return hintService.getHintInfo(puzzleId, hintLevel, userId);
    }

    /**
     * 购买谜题提示
     */
    @Transactional
    public String purchasePuzzleHint(Long puzzleId, String hintLevel, Long userId) {
        return hintService.purchaseHint(puzzleId, hintLevel, userId);
    }

    /**
     * 评价谜题
     */
    @Transactional
    public void ratePuzzle(Long puzzleId, Long userId, Integer rating) {
        // 使用进度服务进行评价
        progressService.ratePuzzle(userId, puzzleId, rating);

        // 更新谜题的平均评分
        Double averageRating = progressService.getPuzzleAverageRating(puzzleId);
        if (averageRating != null) {
            puzzleMapper.updateAverageRating(puzzleId, averageRating);
        }

        log.info("User {} rated puzzle {} with rating {}", userId, puzzleId, rating);
    }

    /**
     * 转换为谜题信息列表
     */
    private List<PuzzleListResponse.PuzzleInfo> convertToPuzzleInfoList(List<Puzzle> puzzles, Long userId) {
        List<PuzzleListResponse.PuzzleInfo> puzzleInfos = new ArrayList<>();

        for (Puzzle puzzle : puzzles) {
            PuzzleListResponse.PuzzleInfo info = new PuzzleListResponse.PuzzleInfo();
            info.setPuzzleId(puzzle.getPuzzleId());
            info.setPuzzleName(puzzle.getPuzzleName());
            info.setDescription(puzzle.getDescription());
            info.setDifficulty(puzzle.getDifficulty());
            info.setRequiredUCoins(puzzle.getRequiredUCoins());
            info.setUCoinsReward(puzzle.getUCoinsReward());
            info.setReasoningPowerReward(puzzle.getReasoningPowerReward());
            info.setPuzzleType(puzzle.getPuzzleType());
            info.setPlayCount(puzzle.getPlayCount());
            info.setCompletionCount(puzzle.getCompletionCount());
            info.setAverageRating(puzzle.getAverageRating());
            info.setIsDaily(puzzle.getIsDaily());
            info.setCreatedAt(puzzle.getCreatedAt());

            // 如果用户已登录，获取用户相关信息
            if (userId != null) {
                // 获取用户对该谜题的状态
                UserPuzzleProgress progress = progressService.getUserPuzzleProgress(userId, puzzle.getPuzzleId());
                if (progress != null) {
                    info.setUserStatus(progress.getStatus());
                    info.setUserRating(progress.getRating());
                } else {
                    info.setUserStatus("NOT_STARTED");
                    info.setUserRating(null);
                }

                // 检查用户是否有足够的U币来游玩
                var user = userService.getUserById(userId);
                info.setCanPlay(user.getUCoins() >= puzzle.getRequiredUCoins());
            }

            puzzleInfos.add(info);
        }

        return puzzleInfos;
    }
}
