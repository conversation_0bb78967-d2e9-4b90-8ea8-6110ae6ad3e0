package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.entity.VerificationCode;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.VerificationCodeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 验证码服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VerificationCodeService {

    private final VerificationCodeMapper verificationCodeMapper;
    private final EmailService emailService;

    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    // 手机号正则表达式（简单版本）
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^1[3-9]\\d{9}$"
    );

    /**
     * 发送验证码
     */
    @Transactional
    public void sendVerificationCode(String target, String type, String purpose) {
        // 1. 验证目标格式
        validateTarget(target, type);

        // 2. 检查发送频率限制
        checkSendingLimit(target, type);

        // 3. 生成验证码
        String code = generateCode();

        // 4. 保存验证码到数据库
        VerificationCode verificationCode = new VerificationCode();
        verificationCode.setTarget(target);
        verificationCode.setCode(code);
        verificationCode.setType(type);
        verificationCode.setPurpose(purpose);

        int result = verificationCodeMapper.insert(verificationCode);
        if (result <= 0) {
            throw new BusinessException("保存验证码失败");
        }

        // 5. 发送验证码
        try {
            if ("EMAIL".equals(type)) {
                sendEmailCode(target, code, purpose);
            } else if ("SMS".equals(type)) {
                sendSmsCode(target, code, purpose);
            } else {
                throw new BusinessException("不支持的验证码类型");
            }
            
            log.info("Verification code sent successfully: target={}, type={}, purpose={}", 
                    target, type, purpose);
        } catch (Exception e) {
            log.error("Failed to send verification code: target={}, type={}, purpose={}", 
                     target, type, purpose, e);
            throw new BusinessException("发送验证码失败，请稍后重试");
        }
    }

    /**
     * 验证验证码
     */
    @Transactional
    public boolean verifyCode(String target, String code, String purpose) {
        VerificationCode verificationCode = verificationCodeMapper.findValidCode(target, code, purpose);
        
        if (verificationCode == null) {
            log.warn("Invalid verification code: target={}, code={}, purpose={}", target, code, purpose);
            return false;
        }

        // 标记为已使用
        verificationCodeMapper.markAsUsed(verificationCode.getCodeId());
        
        log.info("Verification code verified successfully: target={}, purpose={}", target, purpose);
        return true;
    }

    /**
     * 验证目标格式
     */
    private void validateTarget(String target, String type) {
        if (target == null || target.trim().isEmpty()) {
            throw new BusinessException("目标不能为空");
        }

        if ("EMAIL".equals(type)) {
            if (!EMAIL_PATTERN.matcher(target).matches()) {
                throw new BusinessException("邮箱格式不正确");
            }
        } else if ("SMS".equals(type)) {
            if (!PHONE_PATTERN.matcher(target).matches()) {
                throw new BusinessException("手机号格式不正确");
            }
        } else {
            throw new BusinessException("不支持的验证码类型");
        }
    }

    /**
     * 检查发送频率限制
     */
    private void checkSendingLimit(String target, String type) {
        // 检查1分钟内是否已发送
        LocalDateTime oneMinuteAgo = LocalDateTime.now().minusMinutes(1);
        int recentCount = verificationCodeMapper.countByTargetAndTypeSince(target, type, oneMinuteAgo);
        
        if (recentCount > 0) {
            throw new BusinessException("验证码发送过于频繁，请1分钟后再试");
        }

        // 检查1小时内发送次数
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        int hourlyCount = verificationCodeMapper.countByTargetAndTypeSince(target, type, oneHourAgo);
        
        if (hourlyCount >= 5) {
            throw new BusinessException("验证码发送次数过多，请1小时后再试");
        }
    }

    /**
     * 生成6位数字验证码
     */
    private String generateCode() {
        Random random = new Random();
        int code = 100000 + random.nextInt(900000);
        return String.valueOf(code);
    }

    /**
     * 发送邮箱验证码
     */
    private void sendEmailCode(String email, String code, String purpose) {
        String subject = getEmailSubject(purpose);
        String content = getEmailContent(code, purpose);
        emailService.sendSimpleEmail(email, subject, content);
    }

    /**
     * 发送短信验证码（模拟实现）
     */
    private void sendSmsCode(String phone, String code, String purpose) {
        // TODO: 集成短信服务商API
        log.info("SMS verification code would be sent to {}: {}", phone, code);
        // 在实际项目中，这里应该调用短信服务商的API
    }

    /**
     * 获取邮件主题
     */
    private String getEmailSubject(String purpose) {
        switch (purpose) {
            case "REGISTER":
                return "Ultimate推理社 - 注册验证码";
            case "RESET_PASSWORD":
                return "Ultimate推理社 - 密码重置验证码";
            case "BIND_EMAIL":
                return "Ultimate推理社 - 邮箱绑定验证码";
            default:
                return "Ultimate推理社 - 验证码";
        }
    }

    /**
     * 获取邮件内容
     */
    private String getEmailContent(String code, String purpose) {
        String action = getPurposeDescription(purpose);
        return String.format(
            "您好！\n\n" +
            "您正在进行%s操作，验证码为：%s\n\n" +
            "验证码5分钟内有效，请及时使用。如非本人操作，请忽略此邮件。\n\n" +
            "Ultimate推理社团队",
            action, code
        );
    }

    /**
     * 获取用途描述
     */
    private String getPurposeDescription(String purpose) {
        switch (purpose) {
            case "REGISTER":
                return "注册账号";
            case "RESET_PASSWORD":
                return "重置密码";
            case "BIND_EMAIL":
                return "绑定邮箱";
            default:
                return "身份验证";
        }
    }

    /**
     * 清理过期验证码
     */
    @Transactional
    public void cleanExpiredCodes() {
        int count = verificationCodeMapper.cleanExpiredCodes();
        if (count > 0) {
            log.info("Cleaned {} expired verification codes", count);
        }
    }
}
