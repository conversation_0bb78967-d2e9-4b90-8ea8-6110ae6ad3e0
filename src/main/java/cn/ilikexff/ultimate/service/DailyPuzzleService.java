package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.entity.DailyPuzzle;
import cn.ilikexff.ultimate.entity.Puzzle;
import cn.ilikexff.ultimate.entity.UserDailyPuzzleCompletion;
import cn.ilikexff.ultimate.entity.UserPuzzleProgress;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.DailyPuzzleMapper;
import cn.ilikexff.ultimate.mapper.PuzzleMapper;
import cn.ilikexff.ultimate.mapper.UserDailyPuzzleCompletionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

/**
 * 每日一推服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyPuzzleService {

    private final DailyPuzzleMapper dailyPuzzleMapper;
    private final UserDailyPuzzleCompletionMapper completionMapper;
    private final PuzzleMapper puzzleMapper;
    private final UserPuzzleProgressService progressService;
    private final UserService userService;
    private final AchievementService achievementService;

    /**
     * 获取今日一推
     */
    public DailyPuzzleInfo getTodayPuzzle(Long userId) {
        DailyPuzzle dailyPuzzle = dailyPuzzleMapper.findToday();
        if (dailyPuzzle == null) {
            // 如果没有今日一推，自动生成一个
            dailyPuzzle = generateTodayPuzzle();
        }

        // 获取谜题详情
        Puzzle puzzle = puzzleMapper.findById(dailyPuzzle.getPuzzleId());
        if (puzzle == null) {
            throw new BusinessException("每日一推谜题不存在");
        }

        // 检查用户完成状态
        boolean completed = false;
        UserDailyPuzzleCompletion completion = null;
        if (userId != null) {
            completion = completionMapper.findByUserIdAndDailyId(userId, dailyPuzzle.getDailyId());
            completed = completion != null;
        }

        // 获取完成人数
        int completionCount = completionMapper.countByDailyId(dailyPuzzle.getDailyId());

        return new DailyPuzzleInfo(dailyPuzzle, puzzle, completed, completion, completionCount);
    }

    /**
     * 自动生成今日一推
     */
    @Transactional
    public DailyPuzzle generateTodayPuzzle() {
        LocalDate today = LocalDate.now();

        // 检查今天是否已有每日一推
        if (dailyPuzzleMapper.existsByDate(today)) {
            return dailyPuzzleMapper.findByDate(today);
        }

        // 随机选择一个谜题作为今日一推
        List<Puzzle> availablePuzzles = puzzleMapper.findAll();
        if (availablePuzzles.isEmpty()) {
            throw new BusinessException("没有可用的谜题作为每日一推");
        }

        Random random = new Random();
        Puzzle selectedPuzzle = availablePuzzles.get(random.nextInt(availablePuzzles.size()));

        // 创建每日一推记录
        DailyPuzzle dailyPuzzle = new DailyPuzzle();
        dailyPuzzle.setPuzzleDate(today);
        dailyPuzzle.setPuzzleId(selectedPuzzle.getPuzzleId());
        dailyPuzzle.setTheme("每日挑战");
        dailyPuzzle.setDescription("今日精选谜题，完成可获得额外奖励！");
        dailyPuzzle.setBonusUCoins(5);
        dailyPuzzle.setBonusReasoningPower(2);

        int result = dailyPuzzleMapper.insert(dailyPuzzle);
        if (result <= 0) {
            throw new BusinessException("生成每日一推失败");
        }

        log.info("Generated today's daily puzzle: puzzleId={}, dailyId={}",
                selectedPuzzle.getPuzzleId(), dailyPuzzle.getDailyId());

        return dailyPuzzle;
    }

    /**
     * 完成每日一推
     */
    @Transactional
    public void completeDailyPuzzle(Long userId, Long puzzleId) {
        // 获取今日一推
        DailyPuzzle dailyPuzzle = dailyPuzzleMapper.findToday();
        if (dailyPuzzle == null || !dailyPuzzle.getPuzzleId().equals(puzzleId)) {
            // 不是今日一推，不处理
            return;
        }

        // 检查是否已完成
        UserDailyPuzzleCompletion existingCompletion = completionMapper.findByUserIdAndDailyId(userId, dailyPuzzle.getDailyId());
        if (existingCompletion != null) {
            // 已完成，不重复处理
            return;
        }

        // 获取用户谜题进度
        UserPuzzleProgress progress = progressService.getUserPuzzleProgress(userId, puzzleId);
        if (progress == null || !"COMPLETED".equals(progress.getStatus())) {
            // 谜题未完成，不处理
            return;
        }

        // 计算奖励
        Puzzle puzzle = puzzleMapper.findById(puzzleId);
        int totalUCoins = puzzle.getUCoinsReward() + dailyPuzzle.getBonusUCoins();
        int totalReasoningPower = puzzle.getReasoningPowerReward() + dailyPuzzle.getBonusReasoningPower();

        // 创建完成记录
        UserDailyPuzzleCompletion completion = new UserDailyPuzzleCompletion();
        completion.setUserId(userId);
        completion.setDailyId(dailyPuzzle.getDailyId());
        completion.setCompletionDate(LocalDate.now());
        completion.setEarnedUCoins(totalUCoins);
        completion.setEarnedReasoningPower(totalReasoningPower);
        completion.setTimeSpent(progress.getTotalTimeSpent());
        completion.setAttemptsCount(progress.getAttemptsCount());

        int result = completionMapper.insert(completion);
        if (result <= 0) {
            throw new BusinessException("记录每日一推完成失败");
        }

        // 给用户额外奖励
        userService.addUserCoins(userId, dailyPuzzle.getBonusUCoins());
        userService.addReasoningPower(userId, dailyPuzzle.getBonusReasoningPower());

        // 检查每日一推相关成就
        achievementService.checkDailyPuzzleAchievements(userId);

        log.info("User {} completed daily puzzle: dailyId={}, bonusUCoins={}, bonusReasoningPower={}",
                userId, dailyPuzzle.getDailyId(), dailyPuzzle.getBonusUCoins(), dailyPuzzle.getBonusReasoningPower());
    }

    /**
     * 获取用户每日一推统计
     */
    public DailyPuzzleStatistics getUserDailyPuzzleStatistics(Long userId) {
        int totalCompleted = completionMapper.countByUserId(userId);
        int monthlyCompleted = completionMapper.countByUserIdAndMonth(userId, LocalDate.now());
        // 暂时简化连续天数计算
        int consecutiveDays = totalCompleted > 0 ? 1 : 0;
        boolean todayCompleted = completionMapper.hasCompletedToday(userId);

        return new DailyPuzzleStatistics(totalCompleted, monthlyCompleted, consecutiveDays, todayCompleted);
    }

    /**
     * 获取历史每日一推
     */
    public List<DailyPuzzle> getRecentDailyPuzzles(int limit) {
        return dailyPuzzleMapper.findRecent(limit);
    }

    /**
     * 获取用户完成记录
     */
    public List<UserDailyPuzzleCompletion> getUserCompletionHistory(Long userId) {
        return completionMapper.findByUserId(userId);
    }

    /**
     * 每日一推信息类
     */
    public static class DailyPuzzleInfo {
        private DailyPuzzle dailyPuzzle;
        private Puzzle puzzle;
        private boolean completed;
        private UserDailyPuzzleCompletion completion;
        private int completionCount;

        public DailyPuzzleInfo(DailyPuzzle dailyPuzzle, Puzzle puzzle, boolean completed,
                              UserDailyPuzzleCompletion completion, int completionCount) {
            this.dailyPuzzle = dailyPuzzle;
            this.puzzle = puzzle;
            this.completed = completed;
            this.completion = completion;
            this.completionCount = completionCount;
        }

        // Getters
        public DailyPuzzle getDailyPuzzle() { return dailyPuzzle; }
        public Puzzle getPuzzle() { return puzzle; }
        public boolean isCompleted() { return completed; }
        public UserDailyPuzzleCompletion getCompletion() { return completion; }
        public int getCompletionCount() { return completionCount; }
    }

    /**
     * 每日一推统计类
     */
    public static class DailyPuzzleStatistics {
        private int totalCompleted;
        private int monthlyCompleted;
        private int consecutiveDays;
        private boolean todayCompleted;

        public DailyPuzzleStatistics(int totalCompleted, int monthlyCompleted,
                                   int consecutiveDays, boolean todayCompleted) {
            this.totalCompleted = totalCompleted;
            this.monthlyCompleted = monthlyCompleted;
            this.consecutiveDays = consecutiveDays;
            this.todayCompleted = todayCompleted;
        }

        // Getters
        public int getTotalCompleted() { return totalCompleted; }
        public int getMonthlyCompleted() { return monthlyCompleted; }
        public int getConsecutiveDays() { return consecutiveDays; }
        public boolean isTodayCompleted() { return todayCompleted; }
    }
}
