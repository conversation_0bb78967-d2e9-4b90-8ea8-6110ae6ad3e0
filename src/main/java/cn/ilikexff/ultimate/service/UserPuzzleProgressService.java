package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.entity.UserPuzzleProgress;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.UserPuzzleProgressMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户谜题进度服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserPuzzleProgressService {

    private final UserPuzzleProgressMapper progressMapper;

    /**
     * 获取用户对指定谜题的进度
     */
    public UserPuzzleProgress getUserPuzzleProgress(Long userId, Long puzzleId) {
        return progressMapper.findByUserIdAndPuzzleId(userId, puzzleId);
    }

    /**
     * 获取用户所有谜题进度
     */
    public List<UserPuzzleProgress> getUserAllProgress(Long userId) {
        return progressMapper.findByUserId(userId);
    }

    /**
     * 获取用户已完成的谜题进度
     */
    public List<UserPuzzleProgress> getUserCompletedProgress(Long userId) {
        return progressMapper.findCompletedByUserId(userId);
    }

    /**
     * 获取用户进行中的谜题进度
     */
    public List<UserPuzzleProgress> getUserInProgressProgress(Long userId) {
        return progressMapper.findInProgressByUserId(userId);
    }

    /**
     * 开始解谜 - 创建或更新进度记录
     */
    @Transactional
    public UserPuzzleProgress startPuzzle(Long userId, Long puzzleId) {
        UserPuzzleProgress progress = progressMapper.findByUserIdAndPuzzleId(userId, puzzleId);
        
        if (progress == null) {
            // 创建新的进度记录
            progress = new UserPuzzleProgress();
            progress.setUserId(userId);
            progress.setPuzzleId(puzzleId);
            progress.setStatus("IN_PROGRESS");
            progress.setStartTime(LocalDateTime.now());
            progress.setAttemptsCount(0);
            progress.setEarnedUCoins(0);
            progress.setEarnedReasoningPower(0);
            progress.setTotalTimeSpent(0L);
            progress.setHintsUsed(0);
            progress.setCreatedAt(LocalDateTime.now());
            progress.setUpdatedAt(LocalDateTime.now());
            progress.setDeleted(false);
            
            int result = progressMapper.insert(progress);
            if (result <= 0) {
                throw new BusinessException("创建谜题进度失败");
            }
            
            log.info("Created new puzzle progress: userId={}, puzzleId={}", userId, puzzleId);
        } else if ("NOT_STARTED".equals(progress.getStatus())) {
            // 更新状态为进行中
            progress.setStatus("IN_PROGRESS");
            progress.setStartTime(LocalDateTime.now());
            progress.setUpdatedAt(LocalDateTime.now());
            
            int result = progressMapper.update(progress);
            if (result <= 0) {
                throw new BusinessException("更新谜题进度失败");
            }
            
            log.info("Updated puzzle progress to IN_PROGRESS: userId={}, puzzleId={}", userId, puzzleId);
        }
        
        return progress;
    }

    /**
     * 记录答案尝试
     */
    @Transactional
    public void recordAttempt(Long userId, Long puzzleId) {
        int result = progressMapper.incrementAttemptCount(userId, puzzleId);
        if (result <= 0) {
            throw new BusinessException("记录尝试次数失败");
        }
        
        log.debug("Incremented attempt count: userId={}, puzzleId={}", userId, puzzleId);
    }

    /**
     * 完成谜题
     */
    @Transactional
    public void completePuzzle(Long userId, Long puzzleId, Integer earnedUCoins, 
                              Integer earnedReasoningPower, Long totalTimeSpent) {
        
        int result = progressMapper.completePuzzle(userId, puzzleId, earnedUCoins, 
                                                  earnedReasoningPower, totalTimeSpent);
        if (result <= 0) {
            throw new BusinessException("完成谜题记录失败");
        }
        
        log.info("Completed puzzle: userId={}, puzzleId={}, earnedUCoins={}, earnedReasoningPower={}", 
                userId, puzzleId, earnedUCoins, earnedReasoningPower);
    }

    /**
     * 使用提示
     */
    @Transactional
    public void useHint(Long userId, Long puzzleId) {
        int result = progressMapper.incrementHintsUsed(userId, puzzleId);
        if (result <= 0) {
            throw new BusinessException("记录提示使用失败");
        }
        
        log.debug("Used hint: userId={}, puzzleId={}", userId, puzzleId);
    }

    /**
     * 评价谜题
     */
    @Transactional
    public void ratePuzzle(Long userId, Long puzzleId, Integer rating) {
        if (rating < 1 || rating > 5) {
            throw new BusinessException("评分必须在1-5之间");
        }
        
        // 检查用户是否已完成该谜题
        UserPuzzleProgress progress = progressMapper.findByUserIdAndPuzzleId(userId, puzzleId);
        if (progress == null || !"COMPLETED".equals(progress.getStatus())) {
            throw new BusinessException("只能对已完成的谜题进行评价");
        }
        
        int result = progressMapper.updateRating(userId, puzzleId, rating);
        if (result <= 0) {
            throw new BusinessException("评价失败");
        }
        
        log.info("Rated puzzle: userId={}, puzzleId={}, rating={}", userId, puzzleId, rating);
    }

    /**
     * 获取用户完成的谜题数量
     */
    public int getUserCompletedCount(Long userId) {
        return progressMapper.countCompletedByUserId(userId);
    }

    /**
     * 获取用户进行中的谜题数量
     */
    public int getUserInProgressCount(Long userId) {
        return progressMapper.countInProgressByUserId(userId);
    }

    /**
     * 获取谜题的完成人数
     */
    public int getPuzzleCompletedCount(Long puzzleId) {
        return progressMapper.countCompletedByPuzzleId(puzzleId);
    }

    /**
     * 获取谜题的平均评分
     */
    public Double getPuzzleAverageRating(Long puzzleId) {
        return progressMapper.getAverageRatingByPuzzleId(puzzleId);
    }

    /**
     * 计算用户在谜题上花费的时间
     */
    public Long calculateTimeSpent(Long userId, Long puzzleId) {
        UserPuzzleProgress progress = progressMapper.findByUserIdAndPuzzleId(userId, puzzleId);
        if (progress == null || progress.getStartTime() == null) {
            return 0L;
        }
        
        LocalDateTime endTime = progress.getCompletionDate() != null ? 
                               progress.getCompletionDate() : LocalDateTime.now();
        
        return java.time.Duration.between(progress.getStartTime(), endTime).getSeconds();
    }
}
