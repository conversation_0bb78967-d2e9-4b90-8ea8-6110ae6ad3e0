package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.entity.Hint;
import cn.ilikexff.ultimate.entity.UserHintLog;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.HintMapper;
import cn.ilikexff.ultimate.mapper.UserHintLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 提示服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HintService {

    private final HintMapper hintMapper;
    private final UserHintLogMapper userHintLogMapper;
    private final UserService userService;
    private final UserPuzzleProgressService progressService;

    /**
     * 根据ID获取提示
     */
    public Hint getHintById(Long hintId) {
        Hint hint = hintMapper.findById(hintId);
        if (hint == null) {
            throw new BusinessException("提示不存在");
        }
        return hint;
    }

    /**
     * 获取谜题的所有提示
     */
    public List<Hint> getHintsByPuzzleId(Long puzzleId) {
        return hintMapper.findByPuzzleId(puzzleId);
    }

    /**
     * 根据谜题ID和等级获取提示
     */
    public List<Hint> getHintsByPuzzleIdAndLevel(Long puzzleId, String hintLevel) {
        return hintMapper.findByPuzzleIdAndLevel(puzzleId, hintLevel);
    }

    /**
     * 获取用户已购买的提示记录
     */
    public List<UserHintLog> getUserPurchasedHints(Long userId) {
        return userHintLogMapper.findByUserId(userId);
    }

    /**
     * 获取用户在指定谜题上已购买的提示
     */
    public List<UserHintLog> getUserPuzzleHints(Long userId, Long puzzleId) {
        return userHintLogMapper.findByUserIdAndPuzzleId(userId, puzzleId);
    }

    /**
     * 检查用户是否已购买指定提示
     */
    public boolean hasUserPurchasedHint(Long userId, Long hintId) {
        return userHintLogMapper.hasUserPurchasedHint(userId, hintId);
    }

    /**
     * 购买提示
     */
    @Transactional
    public String purchaseHint(Long puzzleId, String hintLevel, Long userId) {
        // 1. 检查用户是否已开始该谜题
        var progress = progressService.getUserPuzzleProgress(userId, puzzleId);
        if (progress == null || "NOT_STARTED".equals(progress.getStatus())) {
            throw new BusinessException("请先开始解谜再购买提示");
        }

        // 2. 获取指定等级的第一个提示
        Hint hint = hintMapper.findFirstByPuzzleIdAndLevel(puzzleId, hintLevel);
        if (hint == null) {
            throw new BusinessException("该等级的提示不存在");
        }

        // 3. 检查用户是否已购买该提示
        if (hasUserPurchasedHint(userId, hint.getHintId())) {
            // 已购买，直接返回提示内容
            return hint.getContent();
        }

        // 4. 检查用户U币是否足够
        var user = userService.getUserById(userId);
        if (user.getUCoins() < hint.getCostUCoins()) {
            throw new BusinessException("U币余额不足，需要 " + hint.getCostUCoins() + " 个U币");
        }

        // 5. 扣除U币
        userService.deductUserCoins(userId, hint.getCostUCoins());

        // 6. 记录购买记录
        UserHintLog hintLog = new UserHintLog();
        hintLog.setUserId(userId);
        hintLog.setHintId(hint.getHintId());
        hintLog.setPurchaseTime(LocalDateTime.now());
        hintLog.setCreatedAt(LocalDateTime.now());
        hintLog.setUpdatedAt(LocalDateTime.now());
        hintLog.setDeleted(false);

        int result = userHintLogMapper.insert(hintLog);
        if (result <= 0) {
            throw new BusinessException("购买提示失败");
        }

        // 7. 更新用户谜题进度中的提示使用次数
        progressService.useHint(userId, puzzleId);

        log.info("User {} purchased hint {} for puzzle {} with {} U-coins",
                userId, hint.getHintId(), puzzleId, hint.getCostUCoins());

        // 8. 返回提示内容
        return hint.getContent();
    }

    /**
     * 获取提示（如果已购买则直接返回，否则返回购买信息）
     */
    public Object getHintInfo(Long puzzleId, String hintLevel, Long userId) {
        // 1. 获取指定等级的第一个提示
        Hint hint = hintMapper.findFirstByPuzzleIdAndLevel(puzzleId, hintLevel);
        if (hint == null) {
            throw new BusinessException("该等级的提示不存在");
        }

        // 2. 检查用户是否已购买该提示
        if (hasUserPurchasedHint(userId, hint.getHintId())) {
            // 已购买，返回提示内容
            return new HintResponse(true, hint.getContent(), 0, "已购买");
        } else {
            // 未购买，返回购买信息
            var user = userService.getUserById(userId);
            boolean canAfford = user.getUCoins() >= hint.getCostUCoins();
            String message = canAfford ? "点击购买提示" : "U币余额不足";

            return new HintResponse(false, null, hint.getCostUCoins(), message);
        }
    }

    /**
     * 获取用户在谜题上的提示统计
     */
    public HintStatistics getUserPuzzleHintStatistics(Long userId, Long puzzleId) {
        int totalHints = hintMapper.countByPuzzleId(puzzleId);
        int purchasedHints = userHintLogMapper.countByUserIdAndPuzzleId(userId, puzzleId);

        int basicHints = hintMapper.countByPuzzleIdAndLevel(puzzleId, "BASIC");
        int mediumHints = hintMapper.countByPuzzleIdAndLevel(puzzleId, "MEDIUM");
        int advancedHints = hintMapper.countByPuzzleIdAndLevel(puzzleId, "ADVANCED");

        return new HintStatistics(totalHints, purchasedHints, basicHints, mediumHints, advancedHints);
    }

    /**
     * 提示响应类
     */
    public static class HintResponse {
        private boolean purchased;
        private String content;
        private Integer cost;
        private String message;

        public HintResponse(boolean purchased, String content, Integer cost, String message) {
            this.purchased = purchased;
            this.content = content;
            this.cost = cost;
            this.message = message;
        }

        // Getters
        public boolean isPurchased() { return purchased; }
        public String getContent() { return content; }
        public Integer getCost() { return cost; }
        public String getMessage() { return message; }
    }

    /**
     * 提示统计类
     */
    public static class HintStatistics {
        private int totalHints;
        private int purchasedHints;
        private int basicHints;
        private int mediumHints;
        private int advancedHints;

        public HintStatistics(int totalHints, int purchasedHints, int basicHints, int mediumHints, int advancedHints) {
            this.totalHints = totalHints;
            this.purchasedHints = purchasedHints;
            this.basicHints = basicHints;
            this.mediumHints = mediumHints;
            this.advancedHints = advancedHints;
        }

        // Getters
        public int getTotalHints() { return totalHints; }
        public int getPurchasedHints() { return purchasedHints; }
        public int getBasicHints() { return basicHints; }
        public int getMediumHints() { return mediumHints; }
        public int getAdvancedHints() { return advancedHints; }
    }
}
