package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.dto.UserProfileResponse;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.entity.Title;
import cn.ilikexff.ultimate.entity.Badge;
import cn.ilikexff.ultimate.entity.UserTitle;
import cn.ilikexff.ultimate.entity.UserBadge;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.UserMapper;
import cn.ilikexff.ultimate.mapper.UserTitleMapper;
import cn.ilikexff.ultimate.mapper.UserBadgeMapper;
import cn.ilikexff.ultimate.mapper.TitleMapper;
import cn.ilikexff.ultimate.mapper.BadgeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserMapper userMapper;
    private final UserPuzzleProgressService progressService;
    private final UserTitleMapper userTitleMapper;
    private final UserBadgeMapper userBadgeMapper;
    private final TitleMapper titleMapper;
    private final BadgeMapper badgeMapper;

    /**
     * 根据ID获取用户信息
     */
    public User getUserById(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    /**
     * 根据用户名获取用户信息
     */
    public User getUserByUsername(String username) {
        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    /**
     * 获取用户详细资料
     */
    public UserProfileResponse getUserProfile(Long userId) {
        User user = getUserById(userId);

        UserProfileResponse profile = new UserProfileResponse();
        profile.setUserId(user.getUserId());
        profile.setUsername(user.getUsername());
        profile.setNickname(user.getNickname());
        profile.setEmail(user.getEmail());
        profile.setPhoneNumber(user.getPhoneNumber());
        profile.setAvatarUrl(user.getAvatarUrl());
        profile.setUCoins(user.getUCoins());
        profile.setReasoningPower(user.getReasoningPower());
        profile.setRegistrationDate(user.getRegistrationDate());
        profile.setLastLoginTime(user.getLastLoginTime());
        profile.setLoginCount(user.getLoginCount());

        // 获取用户统计信息
        profile.setCompletedPuzzlesCount(progressService.getUserCompletedCount(userId));
        profile.setTotalPuzzlesCount(progressService.getUserCompletedCount(userId) + progressService.getUserInProgressCount(userId));

        // 获取成就统计
        profile.setOwnedTitlesCount(userTitleMapper.countByUserId(userId));
        profile.setOwnedBadgesCount(userBadgeMapper.countByUserId(userId));

        // 获取用户称号和勋章
        var userTitles = userTitleMapper.findTitlesByUserId(userId);
        var userBadges = userBadgeMapper.findBadgesByUserId(userId);

        // 转换为DTO格式
        profile.setTitles(convertToTitleInfoList(userTitles, userId));
        profile.setBadges(convertToBadgeInfoList(userBadges));

        return profile;
    }

    /**
     * 分配初始奖励（注册时调用）
     */
    @Transactional
    public void assignInitialRewards(Long userId) {
        // 授予新手侦探称号（注册即可获得）
        Title newbieTitle = titleMapper.findHighestAvailableByReasoningPower(0);
        if (newbieTitle != null) {
            grantTitleToUser(userId, newbieTitle.getTitleId());
            // 设置为当前称号
            userMapper.updateCurrentTitle(userId, newbieTitle.getTitleId());
        }

        // 授予首次登录勋章
        Badge firstLoginBadge = badgeMapper.findByName("首次登录");
        if (firstLoginBadge != null) {
            grantBadgeToUser(userId, firstLoginBadge.getBadgeId());
        }

        log.info("Assigned initial rewards to user {}", userId);
    }

    /**
     * 授予用户称号
     */
    @Transactional
    public void grantTitleToUser(Long userId, Long titleId) {
        // 检查是否已经拥有
        if (userTitleMapper.hasUserTitle(userId, titleId)) {
            return;
        }

        UserTitle userTitle = new UserTitle();
        userTitle.setUserId(userId);
        userTitle.setTitleId(titleId);
        userTitle.setAcquisitionDate(LocalDateTime.now());
        userTitle.setCreatedAt(LocalDateTime.now());
        userTitle.setUpdatedAt(LocalDateTime.now());
        userTitle.setDeleted(false);

        userTitleMapper.insert(userTitle);
    }

    /**
     * 授予用户勋章
     */
    @Transactional
    public void grantBadgeToUser(Long userId, Long badgeId) {
        // 检查是否已经拥有
        if (userBadgeMapper.hasUserBadge(userId, badgeId)) {
            return;
        }

        UserBadge userBadge = new UserBadge();
        userBadge.setUserId(userId);
        userBadge.setBadgeId(badgeId);
        userBadge.setAcquisitionDate(LocalDateTime.now());
        userBadge.setCreatedAt(LocalDateTime.now());
        userBadge.setUpdatedAt(LocalDateTime.now());
        userBadge.setDeleted(false);

        userBadgeMapper.insert(userBadge);
    }

    /**
     * 更新用户基本信息
     */
    @Transactional
    public void updateUserInfo(Long userId, String nickname, String avatarUrl) {
        User user = getUserById(userId);

        if (nickname != null && !nickname.trim().isEmpty()) {
            user.setNickname(nickname.trim());
        }
        if (avatarUrl != null) {
            user.setAvatarUrl(avatarUrl);
        }

        user.setUpdatedAt(LocalDateTime.now());

        int result = userMapper.updateUserInfo(user);
        if (result <= 0) {
            throw new BusinessException("更新用户信息失败");
        }

        log.info("User info updated successfully for userId: {}", userId);
    }

    /**
     * 更新用户U币
     */
    @Transactional
    public void updateUserCoins(Long userId, Integer coins) {
        if (coins < 0) {
            throw new BusinessException("U币数量不能为负数");
        }

        int result = userMapper.updateUCoins(userId, coins);
        if (result <= 0) {
            throw new BusinessException("更新U币失败");
        }

        log.info("User coins updated: userId={}, coins={}", userId, coins);
    }

    /**
     * 增加用户U币
     */
    @Transactional
    public void addUserCoins(Long userId, Integer amount) {
        if (amount <= 0) {
            throw new BusinessException("增加的U币数量必须大于0");
        }

        User user = getUserById(userId);
        Integer newCoins = user.getUCoins() + amount;
        updateUserCoins(userId, newCoins);
    }

    /**
     * 扣除用户U币
     */
    @Transactional
    public void deductUserCoins(Long userId, Integer amount) {
        if (amount <= 0) {
            throw new BusinessException("扣除的U币数量必须大于0");
        }

        User user = getUserById(userId);
        if (user.getUCoins() < amount) {
            throw new BusinessException("U币余额不足");
        }

        Integer newCoins = user.getUCoins() - amount;
        updateUserCoins(userId, newCoins);
    }

    /**
     * 更新用户推理力
     */
    @Transactional
    public void updateUserReasoningPower(Long userId, Integer reasoningPower) {
        if (reasoningPower < 0) {
            throw new BusinessException("推理力不能为负数");
        }

        int result = userMapper.updateReasoningPower(userId, reasoningPower);
        if (result <= 0) {
            throw new BusinessException("更新推理力失败");
        }

        log.info("User reasoning power updated: userId={}, reasoningPower={}", userId, reasoningPower);
    }

    /**
     * 增加用户推理力
     */
    @Transactional
    public void addUserReasoningPower(Long userId, Integer amount) {
        if (amount <= 0) {
            throw new BusinessException("增加的推理力必须大于0");
        }

        User user = getUserById(userId);
        Integer newReasoningPower = user.getReasoningPower() + amount;
        updateUserReasoningPower(userId, newReasoningPower);
    }



    /**
     * 获取推理力排行榜
     */
    public List<User> getReasoningPowerRanking(int limit) {
        return userMapper.findTopUsersByReasoningPower(limit);
    }

    /**
     * 转换称号列表为DTO格式
     */
    private List<UserProfileResponse.TitleInfo> convertToTitleInfoList(List<Title> titles, Long userId) {
        List<UserProfileResponse.TitleInfo> titleInfos = new ArrayList<>();
        User user = userMapper.findById(userId);

        for (Title title : titles) {
            UserProfileResponse.TitleInfo titleInfo = new UserProfileResponse.TitleInfo();
            titleInfo.setTitleId(title.getTitleId());
            titleInfo.setTitleName(title.getTitleName());
            titleInfo.setDescription(title.getDescription());

            // 获取获得时间
            UserTitle userTitle = userTitleMapper.findByUserIdAndTitleId(userId, title.getTitleId());
            if (userTitle != null) {
                titleInfo.setAcquisitionDate(userTitle.getAcquisitionDate());
            }

            // 检查是否为当前称号
            titleInfo.setIsCurrent(user != null && user.getCurrentTitleId() != null &&
                                  user.getCurrentTitleId().equals(title.getTitleId()));

            titleInfos.add(titleInfo);
        }

        return titleInfos;
    }

    /**
     * 转换勋章列表为DTO格式
     */
    private List<UserProfileResponse.BadgeInfo> convertToBadgeInfoList(List<Badge> badges) {
        List<UserProfileResponse.BadgeInfo> badgeInfos = new ArrayList<>();

        for (Badge badge : badges) {
            UserProfileResponse.BadgeInfo badgeInfo = new UserProfileResponse.BadgeInfo();
            badgeInfo.setBadgeId(badge.getBadgeId());
            badgeInfo.setBadgeName(badge.getBadgeName());
            badgeInfo.setIconUrl(badge.getIconUrl());
            badgeInfo.setDescription(badge.getDescription());

            // 这里可以添加获得时间的逻辑
            // UserBadge userBadge = userBadgeMapper.findByUserIdAndBadgeId(userId, badge.getBadgeId());

            badgeInfos.add(badgeInfo);
        }

        return badgeInfos;
    }
}
