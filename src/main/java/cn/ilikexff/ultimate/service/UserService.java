package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.dto.UserProfileResponse;
import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserMapper userMapper;
    private final UserPuzzleProgressService progressService;

    /**
     * 根据ID获取用户信息
     */
    public User getUserById(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    /**
     * 根据用户名获取用户信息
     */
    public User getUserByUsername(String username) {
        User user = userMapper.findByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    /**
     * 获取用户详细资料
     */
    public UserProfileResponse getUserProfile(Long userId) {
        User user = getUserById(userId);

        UserProfileResponse profile = new UserProfileResponse();
        profile.setUserId(user.getUserId());
        profile.setUsername(user.getUsername());
        profile.setNickname(user.getNickname());
        profile.setEmail(user.getEmail());
        profile.setPhoneNumber(user.getPhoneNumber());
        profile.setAvatarUrl(user.getAvatarUrl());
        profile.setUCoins(user.getUCoins());
        profile.setReasoningPower(user.getReasoningPower());
        profile.setRegistrationDate(user.getRegistrationDate());
        profile.setLastLoginTime(user.getLastLoginTime());
        profile.setLoginCount(user.getLoginCount());

        // 获取用户统计信息
        profile.setCompletedPuzzlesCount(progressService.getUserCompletedCount(userId));
        profile.setTotalPuzzlesCount(progressService.getUserCompletedCount(userId) + progressService.getUserInProgressCount(userId));
        profile.setOwnedTitlesCount(0); // TODO: 实现称号统计
        profile.setOwnedBadgesCount(0); // TODO: 实现勋章统计

        // TODO: 获取用户称号和勋章
        profile.setTitles(new ArrayList<>());
        profile.setBadges(new ArrayList<>());

        return profile;
    }

    /**
     * 更新用户基本信息
     */
    @Transactional
    public void updateUserInfo(Long userId, String nickname, String avatarUrl) {
        User user = getUserById(userId);

        if (nickname != null && !nickname.trim().isEmpty()) {
            user.setNickname(nickname.trim());
        }
        if (avatarUrl != null) {
            user.setAvatarUrl(avatarUrl);
        }

        user.setUpdatedAt(LocalDateTime.now());

        int result = userMapper.updateUserInfo(user);
        if (result <= 0) {
            throw new BusinessException("更新用户信息失败");
        }

        log.info("User info updated successfully for userId: {}", userId);
    }

    /**
     * 更新用户U币
     */
    @Transactional
    public void updateUserCoins(Long userId, Integer coins) {
        if (coins < 0) {
            throw new BusinessException("U币数量不能为负数");
        }

        int result = userMapper.updateUCoins(userId, coins);
        if (result <= 0) {
            throw new BusinessException("更新U币失败");
        }

        log.info("User coins updated: userId={}, coins={}", userId, coins);
    }

    /**
     * 增加用户U币
     */
    @Transactional
    public void addUserCoins(Long userId, Integer amount) {
        if (amount <= 0) {
            throw new BusinessException("增加的U币数量必须大于0");
        }

        User user = getUserById(userId);
        Integer newCoins = user.getUCoins() + amount;
        updateUserCoins(userId, newCoins);
    }

    /**
     * 扣除用户U币
     */
    @Transactional
    public void deductUserCoins(Long userId, Integer amount) {
        if (amount <= 0) {
            throw new BusinessException("扣除的U币数量必须大于0");
        }

        User user = getUserById(userId);
        if (user.getUCoins() < amount) {
            throw new BusinessException("U币余额不足");
        }

        Integer newCoins = user.getUCoins() - amount;
        updateUserCoins(userId, newCoins);
    }

    /**
     * 更新用户推理力
     */
    @Transactional
    public void updateUserReasoningPower(Long userId, Integer reasoningPower) {
        if (reasoningPower < 0) {
            throw new BusinessException("推理力不能为负数");
        }

        int result = userMapper.updateReasoningPower(userId, reasoningPower);
        if (result <= 0) {
            throw new BusinessException("更新推理力失败");
        }

        log.info("User reasoning power updated: userId={}, reasoningPower={}", userId, reasoningPower);
    }

    /**
     * 增加用户推理力
     */
    @Transactional
    public void addUserReasoningPower(Long userId, Integer amount) {
        if (amount <= 0) {
            throw new BusinessException("增加的推理力必须大于0");
        }

        User user = getUserById(userId);
        Integer newReasoningPower = user.getReasoningPower() + amount;
        updateUserReasoningPower(userId, newReasoningPower);
    }

    /**
     * 为新用户分配初始奖励
     */
    @Transactional
    public void assignInitialRewards(Long userId) {
        // TODO: 分配初始称号（新手侦探）
        // TODO: 分配初始勋章（首次登录）
        log.info("Initial rewards assigned to user: {}", userId);
    }

    /**
     * 获取推理力排行榜
     */
    public List<User> getReasoningPowerRanking(int limit) {
        return userMapper.findTopUsersByReasoningPower(limit);
    }
}
