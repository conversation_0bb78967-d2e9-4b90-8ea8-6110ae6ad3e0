package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.entity.SignInConfig;
import cn.ilikexff.ultimate.entity.UserSignIn;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.SignInConfigMapper;
import cn.ilikexff.ultimate.mapper.UserSignInMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 签到服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SignInService {

    private final UserSignInMapper userSignInMapper;
    private final SignInConfigMapper signInConfigMapper;
    private final UserService userService;
    private final AchievementService achievementService;

    /**
     * 用户签到
     */
    @Transactional
    public SignInResult signIn(Long userId) {
        LocalDate today = LocalDate.now();
        
        // 检查今天是否已经签到
        if (userSignInMapper.hasSignedInToday(userId, today)) {
            throw new BusinessException("今天已经签到过了");
        }

        // 计算连续签到天数
        int consecutiveDays = calculateConsecutiveDays(userId);
        
        // 获取签到配置（按7天循环）
        int dayInCycle = ((consecutiveDays - 1) % 7) + 1;
        SignInConfig config = signInConfigMapper.findByDayNumber(dayInCycle);
        if (config == null) {
            // 使用默认配置
            config = getDefaultConfig(dayInCycle);
        }

        // 计算奖励
        int totalReward = config.getBaseReward() + config.getBonusReward();
        
        // 创建签到记录
        UserSignIn signIn = new UserSignIn();
        signIn.setUserId(userId);
        signIn.setSignInDate(today);
        signIn.setRewardUCoins(totalReward);
        signIn.setConsecutiveDays(consecutiveDays);
        signIn.setRewardType(config.getRewardType());
        signIn.setIsDoubleReward(config.getBonusReward() > 0);

        int result = userSignInMapper.insert(signIn);
        if (result <= 0) {
            throw new BusinessException("签到失败");
        }

        // 给用户增加U币
        userService.addUserCoins(userId, totalReward);

        // 检查签到相关成就
        achievementService.checkSignInAchievements(userId, consecutiveDays);

        log.info("User {} signed in successfully, consecutive days: {}, reward: {} U-coins", 
                userId, consecutiveDays, totalReward);

        return new SignInResult(consecutiveDays, totalReward, config.getRewardType(), 
                               config.getDescription(), dayInCycle);
    }

    /**
     * 计算连续签到天数
     */
    private int calculateConsecutiveDays(Long userId) {
        UserSignIn latestSignIn = userSignInMapper.findLatestByUserId(userId);
        
        if (latestSignIn == null) {
            // 第一次签到
            return 1;
        }

        LocalDate today = LocalDate.now();
        LocalDate lastSignInDate = latestSignIn.getSignInDate();
        long daysBetween = ChronoUnit.DAYS.between(lastSignInDate, today);

        if (daysBetween == 1) {
            // 连续签到
            return latestSignIn.getConsecutiveDays() + 1;
        } else if (daysBetween > 1) {
            // 中断了，重新开始
            return 1;
        } else {
            // daysBetween == 0，今天已经签到过了（这种情况应该在前面被拦截）
            throw new BusinessException("今天已经签到过了");
        }
    }

    /**
     * 获取默认签到配置
     */
    private SignInConfig getDefaultConfig(int dayNumber) {
        SignInConfig config = new SignInConfig();
        config.setDayNumber(dayNumber);
        config.setBaseReward(5);
        config.setBonusReward(dayNumber == 7 ? 10 : 0);
        config.setRewardType(dayNumber == 7 ? "WEEKLY_BONUS" : "NORMAL");
        config.setDescription("第" + dayNumber + "天签到奖励");
        return config;
    }

    /**
     * 检查用户今天是否已签到
     */
    public boolean hasSignedInToday(Long userId) {
        return userSignInMapper.hasSignedInToday(userId, LocalDate.now());
    }

    /**
     * 获取用户签到统计
     */
    public SignInStatistics getUserSignInStatistics(Long userId) {
        int totalDays = userSignInMapper.countTotalSignInDays(userId);
        int monthlyDays = userSignInMapper.countMonthlySignInDays(userId, LocalDate.now());
        Integer maxConsecutive = userSignInMapper.getMaxConsecutiveDays(userId);
        Integer currentConsecutive = userSignInMapper.getCurrentConsecutiveDays(userId);
        boolean todaySignedIn = hasSignedInToday(userId);

        return new SignInStatistics(
            totalDays, 
            monthlyDays, 
            maxConsecutive != null ? maxConsecutive : 0,
            currentConsecutive != null ? currentConsecutive : 0,
            todaySignedIn
        );
    }

    /**
     * 获取用户本月签到日历
     */
    public List<LocalDate> getMonthlySignInCalendar(Long userId, LocalDate month) {
        return userSignInMapper.getMonthlySignInDates(userId, month);
    }

    /**
     * 获取用户签到历史
     */
    public List<UserSignIn> getUserSignInHistory(Long userId, int limit) {
        List<UserSignIn> allRecords = userSignInMapper.findByUserId(userId);
        if (limit > 0 && allRecords.size() > limit) {
            return allRecords.subList(0, limit);
        }
        return allRecords;
    }

    /**
     * 获取签到配置
     */
    public List<SignInConfig> getSignInConfigs() {
        return signInConfigMapper.findAllActive();
    }

    /**
     * 签到结果类
     */
    public static class SignInResult {
        private int consecutiveDays;
        private int rewardUCoins;
        private String rewardType;
        private String description;
        private int dayInCycle;

        public SignInResult(int consecutiveDays, int rewardUCoins, String rewardType, 
                           String description, int dayInCycle) {
            this.consecutiveDays = consecutiveDays;
            this.rewardUCoins = rewardUCoins;
            this.rewardType = rewardType;
            this.description = description;
            this.dayInCycle = dayInCycle;
        }

        // Getters
        public int getConsecutiveDays() { return consecutiveDays; }
        public int getRewardUCoins() { return rewardUCoins; }
        public String getRewardType() { return rewardType; }
        public String getDescription() { return description; }
        public int getDayInCycle() { return dayInCycle; }
    }

    /**
     * 签到统计类
     */
    public static class SignInStatistics {
        private int totalSignInDays;
        private int monthlySignInDays;
        private int maxConsecutiveDays;
        private int currentConsecutiveDays;
        private boolean todaySignedIn;

        public SignInStatistics(int totalSignInDays, int monthlySignInDays, 
                               int maxConsecutiveDays, int currentConsecutiveDays, 
                               boolean todaySignedIn) {
            this.totalSignInDays = totalSignInDays;
            this.monthlySignInDays = monthlySignInDays;
            this.maxConsecutiveDays = maxConsecutiveDays;
            this.currentConsecutiveDays = currentConsecutiveDays;
            this.todaySignedIn = todaySignedIn;
        }

        // Getters
        public int getTotalSignInDays() { return totalSignInDays; }
        public int getMonthlySignInDays() { return monthlySignInDays; }
        public int getMaxConsecutiveDays() { return maxConsecutiveDays; }
        public int getCurrentConsecutiveDays() { return currentConsecutiveDays; }
        public boolean isTodaySignedIn() { return todaySignedIn; }
    }
}
