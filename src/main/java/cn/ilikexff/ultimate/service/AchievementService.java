package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.entity.*;
import cn.ilikexff.ultimate.exception.BusinessException;
import cn.ilikexff.ultimate.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 成就系统服务（称号和勋章）
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AchievementService {

    private final TitleMapper titleMapper;
    private final BadgeMapper badgeMapper;
    private final UserTitleMapper userTitleMapper;
    private final UserBadgeMapper userBadgeMapper;
    private final UserMapper userMapper;
    private final UserPuzzleProgressService progressService;

    // ==================== 称号相关方法 ====================

    /**
     * 获取所有称号
     */
    public List<Title> getAllTitles() {
        return titleMapper.findAll();
    }

    /**
     * 获取用户拥有的称号
     */
    public List<Title> getUserTitles(Long userId) {
        return userTitleMapper.findTitlesByUserId(userId);
    }

    /**
     * 检查并更新用户称号
     */
    @Transactional
    public List<Title> checkAndUpdateUserTitles(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        List<Title> newTitles = new ArrayList<>();
        
        // 获取用户当前推理力可以获得的所有称号
        List<Title> availableTitles = titleMapper.findAvailableByReasoningPower(user.getReasoningPower());
        
        for (Title title : availableTitles) {
            // 检查用户是否已经拥有该称号
            if (!userTitleMapper.hasUserTitle(userId, title.getTitleId())) {
                // 授予称号
                grantTitleToUser(userId, title.getTitleId());
                newTitles.add(title);
                log.info("Granted title '{}' to user {}", title.getTitleName(), userId);
            }
        }

        // 更新用户当前佩戴的称号为最高等级称号
        updateUserCurrentTitle(userId);

        return newTitles;
    }

    /**
     * 授予用户称号
     */
    @Transactional
    public void grantTitleToUser(Long userId, Long titleId) {
        // 检查是否已经拥有
        if (userTitleMapper.hasUserTitle(userId, titleId)) {
            return;
        }

        UserTitle userTitle = new UserTitle();
        userTitle.setUserId(userId);
        userTitle.setTitleId(titleId);
        userTitle.setAcquisitionDate(LocalDateTime.now());
        userTitle.setCreatedAt(LocalDateTime.now());
        userTitle.setUpdatedAt(LocalDateTime.now());
        userTitle.setDeleted(false);

        int result = userTitleMapper.insert(userTitle);
        if (result <= 0) {
            throw new BusinessException("授予称号失败");
        }
    }

    /**
     * 更新用户当前佩戴的称号
     */
    @Transactional
    public void updateUserCurrentTitle(Long userId) {
        User user = userMapper.findById(userId);
        if (user == null) {
            return;
        }

        // 获取用户推理力可以获得的最高称号
        Title highestTitle = titleMapper.findHighestAvailableByReasoningPower(user.getReasoningPower());
        if (highestTitle != null && userTitleMapper.hasUserTitle(userId, highestTitle.getTitleId())) {
            // 更新用户当前称号
            userMapper.updateCurrentTitle(userId, highestTitle.getTitleId());
        }
    }

    // ==================== 勋章相关方法 ====================

    /**
     * 获取所有勋章
     */
    public List<Badge> getAllBadges() {
        return badgeMapper.findAll();
    }

    /**
     * 获取用户拥有的勋章
     */
    public List<Badge> getUserBadges(Long userId) {
        return userBadgeMapper.findBadgesByUserId(userId);
    }

    /**
     * 授予用户勋章
     */
    @Transactional
    public void grantBadgeToUser(Long userId, Long badgeId) {
        // 检查是否已经拥有
        if (userBadgeMapper.hasUserBadge(userId, badgeId)) {
            return;
        }

        UserBadge userBadge = new UserBadge();
        userBadge.setUserId(userId);
        userBadge.setBadgeId(badgeId);
        userBadge.setAcquisitionDate(LocalDateTime.now());
        userBadge.setCreatedAt(LocalDateTime.now());
        userBadge.setUpdatedAt(LocalDateTime.now());
        userBadge.setDeleted(false);

        int result = userBadgeMapper.insert(userBadge);
        if (result <= 0) {
            throw new BusinessException("授予勋章失败");
        }

        Badge badge = badgeMapper.findById(badgeId);
        if (badge != null) {
            log.info("Granted badge '{}' to user {}", badge.getBadgeName(), userId);
        }
    }

    /**
     * 根据名称授予勋章
     */
    @Transactional
    public void grantBadgeToUserByName(Long userId, String badgeName) {
        Badge badge = badgeMapper.findByName(badgeName);
        if (badge != null) {
            grantBadgeToUser(userId, badge.getBadgeId());
        }
    }

    // ==================== 成就检查方法 ====================

    /**
     * 检查用户注册相关成就
     */
    @Transactional
    public void checkRegistrationAchievements(Long userId) {
        // 授予新手侦探称号（注册即可获得）
        Title newbieTitle = titleMapper.findHighestAvailableByReasoningPower(0);
        if (newbieTitle != null) {
            grantTitleToUser(userId, newbieTitle.getTitleId());
            updateUserCurrentTitle(userId);
        }

        // 授予首次登录勋章
        grantBadgeToUserByName(userId, "首次登录");
    }

    /**
     * 检查用户完成谜题相关成就
     */
    @Transactional
    public void checkPuzzleCompletionAchievements(Long userId, Long puzzleId) {
        int completedCount = progressService.getUserCompletedCount(userId);
        
        // 检查解谜相关勋章
        if (completedCount == 1) {
            grantBadgeToUserByName(userId, "解谜新手");
        } else if (completedCount == 10) {
            grantBadgeToUserByName(userId, "解谜达人");
        } else if (completedCount == 50) {
            grantBadgeToUserByName(userId, "解谜大师");
        }

        // 检查每日一推勋章
        // TODO: 需要检查是否为每日一推谜题
        
        // 检查推理力相关称号
        checkAndUpdateUserTitles(userId);
    }

    /**
     * 检查用户签到相关成就
     */
    @Transactional
    public void checkSignInAchievements(Long userId, int consecutiveDays) {
        if (consecutiveDays == 7) {
            grantBadgeToUserByName(userId, "连续签到7天");
        } else if (consecutiveDays == 30) {
            grantBadgeToUserByName(userId, "连续签到30天");
        }
    }

    /**
     * 获取用户成就统计
     */
    public AchievementStatistics getUserAchievementStatistics(Long userId) {
        int titleCount = userTitleMapper.countByUserId(userId);
        int badgeCount = userBadgeMapper.countByUserId(userId);
        int totalTitles = titleMapper.count();
        int totalBadges = badgeMapper.count();
        
        return new AchievementStatistics(titleCount, badgeCount, totalTitles, totalBadges);
    }

    /**
     * 成就统计类
     */
    public static class AchievementStatistics {
        private int ownedTitles;
        private int ownedBadges;
        private int totalTitles;
        private int totalBadges;

        public AchievementStatistics(int ownedTitles, int ownedBadges, int totalTitles, int totalBadges) {
            this.ownedTitles = ownedTitles;
            this.ownedBadges = ownedBadges;
            this.totalTitles = totalTitles;
            this.totalBadges = totalBadges;
        }

        // Getters
        public int getOwnedTitles() { return ownedTitles; }
        public int getOwnedBadges() { return ownedBadges; }
        public int getTotalTitles() { return totalTitles; }
        public int getTotalBadges() { return totalBadges; }
        public double getTitleProgress() { return totalTitles > 0 ? (double) ownedTitles / totalTitles : 0; }
        public double getBadgeProgress() { return totalBadges > 0 ? (double) ownedBadges / totalBadges : 0; }
    }
}
