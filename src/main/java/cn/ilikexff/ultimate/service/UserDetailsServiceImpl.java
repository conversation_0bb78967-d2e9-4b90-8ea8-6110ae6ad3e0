package cn.ilikexff.ultimate.service;

import cn.ilikexff.ultimate.entity.User;
import cn.ilikexff.ultimate.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Spring Security 用户详情服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserMapper userMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("Loading user by username: {}", username);
        
        User user = userMapper.findByUsername(username);
        if (user == null) {
            // 尝试通过邮箱查找
            user = userMapper.findByEmail(username);
        }
        if (user == null) {
            // 尝试通过手机号查找
            user = userMapper.findByPhoneNumber(username);
        }
        
        if (user == null) {
            log.warn("User not found with username: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        if (!"ACTIVE".equals(user.getStatus())) {
            log.warn("User account is not active: {}", username);
            throw new UsernameNotFoundException("用户账户已被禁用: " + username);
        }

        return new UserPrincipal(user);
    }

    /**
     * 用户主体类，实现 UserDetails 接口
     */
    public static class UserPrincipal implements UserDetails {
        private final User user;

        public UserPrincipal(User user) {
            this.user = user;
        }

        public User getUser() {
            return user;
        }

        @Override
        public Collection<? extends GrantedAuthority> getAuthorities() {
            Collection<GrantedAuthority> authorities = new ArrayList<>();
            // 默认给所有用户 USER 角色
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
            return authorities;
        }

        @Override
        public String getPassword() {
            return user.getPasswordHash();
        }

        @Override
        public String getUsername() {
            return user.getUsername();
        }

        @Override
        public boolean isAccountNonExpired() {
            return true;
        }

        @Override
        public boolean isAccountNonLocked() {
            return !"BANNED".equals(user.getStatus());
        }

        @Override
        public boolean isCredentialsNonExpired() {
            return true;
        }

        @Override
        public boolean isEnabled() {
            return "ACTIVE".equals(user.getStatus());
        }
    }
}
