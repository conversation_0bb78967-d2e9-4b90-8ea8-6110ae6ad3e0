package cn.ilikexff.ultimate.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI 3 配置类
 */
@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI ultimateOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Ultimate推理社 API")
                        .description("Ultimate推理社后端API接口文档")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("Ultimate推理社开发团队")
                                .email("<EMAIL>")
                                .url("https://ultimate.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))
                .components(new Components()
                        .addSecuritySchemes("bearerAuth", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT认证token")))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
    }
}
