package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 勋章实体类
 */
@Data
public class Badge {
    
    private Long badgeId;
    private String badgeName;
    private String iconUrl;
    private String description;
    private String acquisitionCondition; // 获取条件描述
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public Badge() {
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
