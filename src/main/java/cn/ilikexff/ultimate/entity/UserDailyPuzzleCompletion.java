package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户每日一推完成记录实体类
 */
@Data
public class UserDailyPuzzleCompletion {
    
    private Long completionId;
    private Long userId;
    private Long dailyId; // 每日一推ID
    private LocalDate completionDate; // 完成日期
    private Integer earnedUCoins; // 获得的U币（基础+额外）
    private Integer earnedReasoningPower; // 获得的推理力（基础+额外）
    private Long timeSpent; // 花费时间（秒）
    private Integer attemptsCount; // 尝试次数
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public UserDailyPuzzleCompletion() {
        this.completionDate = LocalDate.now();
        this.earnedUCoins = 0;
        this.earnedReasoningPower = 0;
        this.timeSpent = 0L;
        this.attemptsCount = 0;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
