package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户签到记录实体类
 */
@Data
public class UserSignIn {
    
    private Long signInId;
    private Long userId;
    private LocalDate signInDate; // 签到日期
    private Integer rewardUCoins; // 获得的U币奖励
    private Integer consecutiveDays; // 连续签到天数
    private Boolean isDoubleReward; // 是否双倍奖励
    private String rewardType; // 奖励类型：NORMAL, WEEKLY_BONUS, MONTHLY_BONUS
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public UserSignIn() {
        this.signInDate = LocalDate.now();
        this.rewardUCoins = 0;
        this.consecutiveDays = 1;
        this.isDoubleReward = false;
        this.rewardType = "NORMAL";
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
