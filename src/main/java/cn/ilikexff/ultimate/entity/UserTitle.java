package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户称号关联实体类
 */
@Data
public class UserTitle {
    
    private Long id;
    private Long userId;
    private Long titleId;
    private LocalDateTime acquisitionDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public UserTitle() {
        this.deleted = false;
        this.acquisitionDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
