package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 每日一推实体类
 */
@Data
public class DailyPuzzle {
    
    private Long dailyId;
    private LocalDate puzzleDate; // 推送日期
    private Long puzzleId; // 谜题ID
    private String theme; // 主题（可选）
    private String description; // 推荐理由
    private Integer bonusUCoins; // 额外U币奖励
    private Integer bonusReasoningPower; // 额外推理力奖励
    private Boolean isActive; // 是否启用
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public DailyPuzzle() {
        this.puzzleDate = LocalDate.now();
        this.bonusUCoins = 5; // 默认额外5个U币
        this.bonusReasoningPower = 2; // 默认额外2推理力
        this.isActive = true;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
