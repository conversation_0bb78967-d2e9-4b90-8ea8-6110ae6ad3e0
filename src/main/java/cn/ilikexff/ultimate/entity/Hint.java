package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 提示实体类
 */
@Data
public class Hint {
    
    private Long hintId;
    private Long puzzleId;
    private String hintLevel; // BASIC, MEDIUM, ADVANCED
    private String content;
    private Integer costUCoins;
    private Integer orderIndex; // 同等级提示的排序
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public Hint() {
        this.orderIndex = 1;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
