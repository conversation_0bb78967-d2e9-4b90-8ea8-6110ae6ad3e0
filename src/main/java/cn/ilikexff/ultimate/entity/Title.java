package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 称号实体类
 */
@Data
public class Title {
    
    private Long titleId;
    private String titleName;
    private String description;
    private Integer minReasoningPower; // 获得所需最低推理力
    private String acquisitionCondition; // 获取条件描述
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public Title() {
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
