package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 签到配置实体类
 */
@Data
public class SignInConfig {
    
    private Long configId;
    private Integer dayNumber; // 第几天（1-7为一周循环）
    private Integer baseReward; // 基础奖励U币
    private Integer bonusReward; // 额外奖励U币
    private String rewardType; // 奖励类型：NORMAL, WEEKLY_BONUS, MONTHLY_BONUS
    private String description; // 奖励描述
    private Boolean isActive; // 是否启用
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public SignInConfig() {
        this.baseReward = 5;
        this.bonusReward = 0;
        this.rewardType = "NORMAL";
        this.isActive = true;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
