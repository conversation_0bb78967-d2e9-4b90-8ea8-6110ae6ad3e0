package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
public class User {
    
    private Long userId;
    private String username;
    private String passwordHash;
    private String email;
    private String phoneNumber;
    private String qqOpenid;
    private String nickname;
    private String avatarUrl;
    private Integer uCoins;
    private Integer reasoningPower;
    private Long currentTitleId;
    private LocalDateTime registrationDate;
    private LocalDateTime lastLoginTime;
    private Integer loginCount;
    private String status; // ACTIVE, BANNED, INACTIVE
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public User() {
        this.uCoins = 0;
        this.reasoningPower = 0;
        this.loginCount = 0;
        this.status = "ACTIVE";
        this.deleted = false;
        this.registrationDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
