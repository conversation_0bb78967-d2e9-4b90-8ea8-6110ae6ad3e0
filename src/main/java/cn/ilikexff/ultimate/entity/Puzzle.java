package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 谜题实体类
 */
@Data
public class Puzzle {
    
    private Long puzzleId;
    private String puzzleName;
    private String description;
    private String backgroundStory;
    private String clues;
    private String difficulty; // EASY, MEDIUM, HARD, EXPERT
    private Integer requiredUCoins;
    private Integer uCoinsReward;
    private Integer reasoningPowerReward;
    private String answer;
    private String puzzleType; // TEXT, IMAGE, AUDIO, VIDEO, MIXED
    private Long createdBy;
    private String status; // DRAFT, PUBLISHED, ARCHIVED
    private Integer playCount;
    private Integer completionCount;
    private Double averageRating;
    private Boolean isDaily;
    private LocalDate dailyDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public Puzzle() {
        this.requiredUCoins = 0;
        this.uCoinsReward = 0;
        this.reasoningPowerReward = 0;
        this.puzzleType = "TEXT";
        this.status = "DRAFT";
        this.playCount = 0;
        this.completionCount = 0;
        this.isDaily = false;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
