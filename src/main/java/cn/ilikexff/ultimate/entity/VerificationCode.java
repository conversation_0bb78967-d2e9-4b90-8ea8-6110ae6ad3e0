package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 验证码实体类
 */
@Data
public class VerificationCode {
    
    private Long codeId;
    private String target; // 目标手机号或邮箱
    private String code; // 验证码
    private String type; // 类型：SMS, EMAIL
    private String purpose; // 用途：REGISTER, RESET_PASSWORD, BIND_PHONE, BIND_EMAIL
    private Boolean used; // 是否已使用
    private LocalDateTime expiresAt; // 过期时间
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public VerificationCode() {
        this.used = false;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        // 默认5分钟过期
        this.expiresAt = LocalDateTime.now().plusMinutes(5);
    }
}
