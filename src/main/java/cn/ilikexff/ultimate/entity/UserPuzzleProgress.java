package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户谜题进度实体类
 */
@Data
public class UserPuzzleProgress {
    
    private Long progressId;
    private Long userId;
    private Long puzzleId;
    private String status; // NOT_STARTED, IN_PROGRESS, COMPLETED
    private Integer attemptsCount;
    private LocalDateTime completionDate;
    private Integer earnedUCoins;
    private Integer earnedReasoningPower;
    private LocalDateTime startTime;
    private Long totalTimeSpent; // 总耗时（秒）
    private Integer hintsUsed;
    private Integer rating; // 用户对谜题的评分 1-5
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public UserPuzzleProgress() {
        this.status = "NOT_STARTED";
        this.attemptsCount = 0;
        this.earnedUCoins = 0;
        this.earnedReasoningPower = 0;
        this.hintsUsed = 0;
        this.deleted = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
