package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户提示记录实体类
 */
@Data
public class UserHintLog {
    
    private Long logId;
    private Long userId;
    private Long hintId;
    private LocalDateTime purchaseTime;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public UserHintLog() {
        this.deleted = false;
        this.purchaseTime = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
