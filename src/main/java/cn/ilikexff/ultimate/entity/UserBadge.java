package cn.ilikexff.ultimate.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户勋章关联实体类
 */
@Data
public class UserBadge {
    
    private Long id;
    private Long userId;
    private Long badgeId;
    private LocalDateTime acquisitionDate;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean deleted;

    public UserBadge() {
        this.deleted = false;
        this.acquisitionDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
