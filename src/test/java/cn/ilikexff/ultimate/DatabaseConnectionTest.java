package cn.ilikexff.ultimate;

import cn.ilikexff.ultimate.mapper.UserMapper;
import cn.ilikexff.ultimate.mapper.PuzzleMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 数据库连接测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class DatabaseConnectionTest {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PuzzleMapper puzzleMapper;

    @Test
    public void testDatabaseConnection() throws Exception {
        try (Connection connection = dataSource.getConnection()) {
            System.out.println("数据库连接成功！");
            System.out.println("数据库URL: " + connection.getMetaData().getURL());
            System.out.println("数据库用户: " + connection.getMetaData().getUserName());
        }
    }

    @Test
    public void testUserMapper() {
        // 测试查询用户总数
        try {
            var users = userMapper.findTopUsersByReasoningPower(10);
            System.out.println("用户Mapper测试成功，查询到 " + users.size() + " 个用户");
        } catch (Exception e) {
            System.out.println("用户Mapper测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testPuzzleMapper() {
        // 测试查询谜题总数
        try {
            int count = puzzleMapper.countPublishedPuzzles();
            System.out.println("谜题Mapper测试成功，已发布谜题数量: " + count);
        } catch (Exception e) {
            System.out.println("谜题Mapper测试失败: " + e.getMessage());
        }
    }
}
