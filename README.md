# 🧩 Ultimate推理社

一个基于Spring Boot的推理谜题社区平台，为推理爱好者提供丰富的谜题挑战和完整的激励体系。

## ✨ 核心功能

### 🔐 用户系统
- **用户认证**：JWT + Spring Security，支持注册/登录
- **实时验证**：用户名/邮箱可用性检查
- **验证码系统**：邮箱验证码，支持频率限制
- **用户资料**：完整的用户信息管理

### 🧩 谜题系统
- **谜题管理**：列表浏览、详情查看、难度分级
- **解答系统**：在线答题、进度跟踪、尝试记录
- **提示系统**：分级提示、U币购买、防重复扣费
- **评价系统**：谜题评分、完成统计

### 🏆 激励体系
- **U币经济**：虚拟货币系统，解谜获得、购买提示消费
- **推理力等级**：基于解谜表现的能力评估
- **称号系统**：5个等级的推理能力认证
- **勋章系统**：7种不同获得条件的成就勋章

### 📅 每日活动
- **每日签到**：连续签到奖励、周奖励机制
- **每日一推**：每日推荐谜题、额外奖励、专属勋章
- **活动统计**：完成记录、连续天数、月度统计

### 📖 API文档
- **在线文档**：SpringDoc + OpenAPI 3
- **接口测试**：完整的API测试界面
- **访问地址**：`http://localhost:8080/swagger-ui.html`

## 🛠️ 技术栈

### 后端技术
- **框架**：Spring Boot 3.5.0
- **安全**：Spring Security 6 + JWT
- **数据库**：MySQL 8.0 + MyBatis
- **缓存**：Redis
- **构建**：Gradle
- **文档**：SpringDoc OpenAPI 3

### 数据库设计
- **用户表**：用户信息、认证数据
- **谜题表**：谜题内容、难度、奖励
- **进度表**：用户解谜进度、尝试记录
- **成就表**：称号、勋章、用户关联
- **活动表**：签到记录、每日一推

## 🚀 快速开始

### 环境要求
- Java 17+
- MySQL 8.0+
- Redis 6.0+
- Gradle 7.0+

### 安装步骤

1. **克隆项目**
```bash
<NAME_EMAIL>:08820048/Ultimate.git
cd Ultimate
```

2. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE ultimate_reasoning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入表结构和初始数据
mysql -u root -p ultimate_reasoning < src/main/resources/sql/schema.sql
mysql -u root -p ultimate_reasoning < src/main/resources/sql/init_data.sql
```

3. **配置应用**
```yaml
# src/main/resources/application.yml
spring:
  datasource:
    url: **********************************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
```

4. **启动应用**
```bash
./gradlew bootRun
```

5. **访问应用**
- API文档：http://localhost:8080/swagger-ui.html
- 应用端口：http://localhost:8080

## 📊 功能演示

### 用户注册登录
```bash
# 检查用户名可用性
curl -X GET "http://localhost:8080/api/auth/check-username?username=testuser"

# 用户注册
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"123456","email":"<EMAIL>"}'

# 用户登录
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"123456"}'
```

### 谜题解答
```bash
# 获取谜题列表
curl -X GET http://localhost:8080/api/puzzles/list

# 开始解谜
curl -X POST http://localhost:8080/api/puzzles/1/start \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 提交答案
curl -X POST http://localhost:8080/api/puzzles/1/submit \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"answer":"your_answer"}'
```

### 每日活动
```bash
# 获取今日一推
curl -X GET http://localhost:8080/api/daily-puzzle/today

# 每日签到
curl -X POST http://localhost:8080/api/signin/daily \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🎯 项目特色

### 完整的激励体系
- **多层次奖励**：U币、推理力、称号、勋章
- **自动触发**：完成谜题自动检查成就
- **进阶机制**：推理力达标自动升级称号

### 用户体验优化
- **实时反馈**：用户名邮箱实时检查
- **防重复操作**：签到、提示购买防重复
- **友好提示**：详细的错误信息和操作指引

### 数据统计完善
- **个人统计**：解谜数量、签到天数、成就进度
- **排行榜**：推理力排行、解谜数量排行
- **活动记录**：完整的用户行为记录

## 📈 开发进度

### ✅ 已完成功能
- [x] 用户认证系统
- [x] 谜题管理系统
- [x] 用户进度系统
- [x] 提示系统
- [x] 称号勋章系统
- [x] 每日签到系统
- [x] 每日一推功能
- [x] 用户名/邮箱检查
- [x] 验证码系统
- [x] API文档系统

### 🚧 开发中功能
- [ ] QQ登录功能
- [ ] 用户信息管理完善
- [ ] 忘记密码找回

### 📋 计划功能
- [ ] 社区功能（评论、讨论）
- [ ] 后台管理系统
- [ ] 数据分析统计
- [ ] 移动端适配

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues: [项目Issues](https://github.com/08820048/Ultimate/issues)
- Email: <EMAIL>

---

**Ultimate推理社** - 让推理成为一种乐趣！🧩✨
