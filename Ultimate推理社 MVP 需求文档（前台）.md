## Ultimate推理社 MVP 需求文档（前台）

### 1. 项目概述

**1.1 项目名称:** Ultimate推理社

**1.2 项目愿景:** 打造一个集推理闯关、社交互动、成就展示为一体的在线推理社区，提升用户推理能力，提供沉浸式解谜体验。

**1.3 MVP目标:**

- 验证核心玩法和用户需求。
- 构建稳定可用的基础平台。
- 吸引首批用户并收集反馈。

**1.4 技术栈:**

- 后端: Java + Redis + MySQL
- 前端: Vite

### 2. 用户角色

- **访客:** 未登录用户。
- **注册用户:** 已登录用户。

### 3. 功能需求

#### 3.1 核心功能模块

**3.1.1 用户系统**

- 注册:
  - 手机号/邮箱注册（验证码）。
  - 密码设置（强度校验）。
  - 用户协议同意。
- 登录:
  - 手机号/邮箱 + 密码登录。
  - QQ 授权登录（OAuth2）。
  - 记住登录状态。
  - 忘记密码（手机号/邮箱找回）。
- 用户信息管理:
  - 查看个人主页（昵称、头像、U币、推理力、称号、勋章、解谜进度）。
  - 修改昵称、头像。
  - 绑定/解绑手机号、邮箱。
  - 修改密码。
  - 个人成就展示（已完成谜题数、最高推理力等）。

**3.1.2 谜题系统**

- 谜题列表:
  - 展示所有可玩谜题，包括：谜题名称、难度、所需 U 币（如果需要）、已玩人数、评分。
  - 支持按难度、类型、最新等条件筛选和排序。
  - 显示谜题状态：未开始、进行中、已完成。
- 解密闯关:
  - 进入谜题后显示谜题描述、背景故事、线索。
  - 用户输入答案，系统校验。
  - 答案正确则闯关成功，获得U币和推理力奖励。
  - 答案错误则提示“答案错误”，可继续尝试。
  - 支持多步解谜（如果谜题设计需要）。
  - 解密帮助:
    - 用户可消耗指定数量的 U 币获取提示（例如：提示一个关键词，或者指出线索中的关键点）。
    - 提示类型：初级提示、中级提示、高级提示（不同级别消耗不同 U 币）。
    - 获取提示后，U 币实时扣除。

**3.1.3 U币与推理力系统**

- U币获取:
  - 注册奖励。
  - 完成谜题（不同难度谜题奖励不同数量 U 币）。
  - 每日签到（连续签到奖励递增）。
  - 其他未来可扩展的任务（例如：分享、邀请）。
- U币消耗:
  - 获取解密帮助。
  - 兑换会员（MVP阶段可简化为单一会员等级，或不开放兑换）。
  - 兑换称号/勋章（MVP阶段可简化）。
- 推理力获取:
  - 完成谜题（不同难度谜题奖励不同数量推理力）。
  - 推理力与谜题难度和完成度正相关。
- 推理力排名:
  - 全站推理力排行榜，实时更新。
  - 显示用户昵称、头像、推理力。
  - 支持查看前 N 名。

**3.1.4 每日一推**

- 每天发布一个新的推理谜题。
- 用户当天首次完成可获得额外 U 币和推理力奖励。
- 谜题难度可控。

**3.1.5 称号与勋章系统 (MVP 阶段简化)**

- 称号:
  - 根据推理力等级自动获得（例如：初级侦探、资深推理家）。
  - 通过特定成就获得（例如：完成所有初级谜题）。
  - 用户可在个人主页选择展示的称号。
- 勋章:
  - 通过完成特定任务或活动获得（例如：首次登录、连续签到 N 天）。
  - 用户可在个人主页展示获得的勋章。

#### 3.2 辅助功能

- 公告/通知:
  - 发布系统公告、更新通知等。
  - 站内信通知用户重要信息。
- 用户反馈/举报:
  - 提供渠道让用户提交Bug、建议或举报不良内容。
- 后台管理 (MVP 阶段简化)
  - 谜题管理：增删改查谜题信息、上传谜题内容、设置答案、难度、奖励。
  - 用户管理：查看用户信息、U币、推理力、封禁等。
  - 公告发布。

### 4. 非功能性需求

**4.1 性能:**

- 页面加载速度快，响应及时。
- 并发用户访问能力良好，保证系统稳定性。
- 数据库查询优化，保证谜题加载、排行榜查询效率。

**4.2 可扩展性:**

- **架构设计:** 采用微服务或模块化设计，方便后续功能扩展和团队协作。
- **数据模型:** U币、推理力、称号、勋章等数据模型设计应具备良好的可扩展性，支持未来新增类型或规则。
- **谜题类型:** 支持不同类型的谜题（文字、图片、音频、视频等），谜题数据结构设计灵活。
- **活动系统:** 预留活动接口，方便未来运营活动接入。

**4.3 安全性:**

- 用户密码加密存储。
- 防止SQL注入、XSS攻击。
- API接口鉴权和数据传输加密。
- QQ登录采用OAuth2标准协议。

**4.4 可维护性:**

- 代码结构清晰，注释完善。
- 日志记录全面，方便故障排查。
- 部署和升级流程简化。

**4.5 用户体验 (UX):**

- 界面简洁明了，操作流畅。
- 谜题流程引导清晰。
- 提示信息友好，反馈及时。

### 5. 技术选型与考虑

- 后端 (Java):
  - Spring Boot: 快速开发、生态丰富。
  - MyBatis/JPA: 数据持久层框架。
  - Redis: 缓存（U币、推理力、排行榜等高频访问数据）、分布式锁、消息队列（用于异步任务）。
  - MySQL: 关系型数据库，存储用户、谜题、订单等核心业务数据。
  - Spring Security: 用户认证与授权。
  - Maven/Gradle: 项目构建工具。
- 前端 (Vite):
  - Vue.js/React.js: 前端框架，选择一个。
  - Axios: HTTP请求库。
  - UI组件库: Ant Design Vue/Element UI 或类似。
- **部署:** Docker 容器化部署，方便快速部署和扩展。

### 6. 核心数据结构 (初步设计)

- 用户表 (User):

  - `user_id` (PK)
  - `username`
  - `password_hash`
  - `email`
  - `phone_number`
  - `qq_openid` (用于QQ登录)
  - `nickname`
  - `avatar_url`
  - `u_coins` (U币数量)
  - `reasoning_power` (推理力)
  - `current_title_id` (当前佩戴称号ID)
  - `registration_date`

- 谜题表 (Puzzle):

  - `puzzle_id` (PK)
  - `puzzle_name`
  - `description`
  - `difficulty` (难度等级)
  - `required_u_coins` (所需U币，可为0)
  - `u_coins_reward` (完成奖励U币)
  - `reasoning_power_reward` (完成奖励推理力)
  - `answer` (谜题答案，考虑加密存储或多步答案)
  - `puzzle_type` (文字、图片等)
  - `created_by`
  - `creation_date`
  - `status` (草稿、发布等)

- 用户谜题进度表 (UserPuzzleProgress):

  - `progress_id` (PK)
  - `user_id` (FK)
  - `puzzle_id` (FK)
  - `status` (未开始、进行中、已完成)
  - `attempts_count` (尝试次数)
  - `completion_date`
  - `earned_u_coins` (本次完成获得U币)
  - `earned_reasoning_power` (本次完成获得推理力)

- 提示表 (Hint):

  - `hint_id` (PK)
  - `puzzle_id` (FK)
  - `hint_level` (初级、中级、高级)
  - `content` (提示内容)
  - `cost_u_coins` (消耗U币)

- 用户提示记录表 (UserHintLog):

  - `log_id` (PK)
  - `user_id` (FK)
  - `hint_id` (FK)
  - `purchase_time`

- 称号表 (Title):

  - `title_id` (PK)
  - `title_name`
  - `description`
  - `min_reasoning_power` (获得所需最低推理力)
  - `acquisition_condition` (获取条件描述，例如：完成所有初级谜题)

- 勋章表 (Badge):

  - `badge_id` (PK)
  - `badge_name`
  - `icon_url`
  - `description`
  - `acquisition_condition` (获取条件描述)

- 用户称号/勋章关联表 (UserTitle/UserBadge):

   (多对多关系)

  - `user_id` (FK)
  - `title_id` (FK)
  - `badge_id` (FK)
  - `acquisition_date`

### 7. 后续扩展性考虑

- **谜题类型多样化:** 引入更多富媒体谜题，如图片、音频、视频谜题，甚至AR/VR谜题。
- 社区功能:
  - 谜题评论、讨论区。
  - 用户私信、好友系统。
  - 社团/小组功能。
  - 用户可创作谜题并发布。
- **社交分享:** 支持将成就、谜题分享到主流社交平台。
- **活动系统:** 引入更多线上/线下活动，例如限时挑战、主题周。
- **商城系统:** U币可兑换更多虚拟物品，如装扮、个性化主题。
- **广告/赞助:** 引入广告或赞助机制，增加收入来源。
- **AI辅助:** 探索AI生成谜题、AI提示等。
- **国际化:** 支持多语言。

### 8. 验收标准 (MVP)

- 用户能够成功注册、登录（包括QQ登录）。
- 用户能够浏览谜题列表，并进入谜题进行解密。
- 用户输入正确答案后，系统能正确判断并给予U币和推理力奖励。
- 用户能够消耗U币获取解密帮助。
- 每日一推功能正常，用户可完成并获得奖励。
- 用户个人主页能正确展示U币、推理力、称号、勋章信息。
- 全站推理力排行榜能正常显示并更新。
- 基本的用户信息修改功能正常。
- 系统运行稳定，无明显Bug。

### 9. 风险与挑战

- **谜题内容生产:** 持续提供高质量、有吸引力的谜题内容。
- **用户粘性:** 如何保持用户对平台的兴趣和活跃度。
- **安全性:** 防范作弊行为，保护用户数据安全。
- **推广运营:** 如何吸引新用户并进行有效的社区运营。
- **性能优化:** 随着用户量增长，如何保证系统性能。

